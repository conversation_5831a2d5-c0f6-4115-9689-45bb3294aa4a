#!/bin/bash
#
# Fix Script for deploy-hauling-qr-ubuntu.sh
# This script fixes the remaining issues found in the final test
#

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_header() {
    echo -e "\n${CYAN}========================================"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}========================================${NC}"
}

log_section() {
    echo -e "\n${BLUE}--- $1 ---${NC}"
}

log_fix() {
    echo -e "${YELLOW}[FIX]${NC} $1"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Function to check if a function exists in the script
check_function_exists() {
    local script_file="$1"
    local function_name="$2"
    
    if grep -q "^$function_name()" "$script_file"; then
        return 0
    else
        return 1
    fi
}

# Function to add missing functions to the script
add_missing_function() {
    local script_file="$1"
    local function_name="$2"
    local function_code="$3"
    
    # Find a good place to add the function (before the main function)
    local insert_line=$(grep -n "^main()" "$script_file" | cut -d: -f1)
    
    if [[ -z "$insert_line" ]]; then
        # If main() not found, try to find parse_arguments()
        insert_line=$(grep -n "^parse_arguments()" "$script_file" | cut -d: -f1)
    fi
    
    if [[ -z "$insert_line" ]]; then
        # If still not found, add to the end of the file
        insert_line=$(wc -l < "$script_file")
    fi
    
    # Create a temporary file with the function added
    local temp_file=$(mktemp)
    head -n $((insert_line - 1)) "$script_file" > "$temp_file"
    echo -e "\n$function_code\n" >> "$temp_file"
    tail -n +$((insert_line)) "$script_file" >> "$temp_file"
    
    # Replace the original file
    cp "$temp_file" "$script_file"
    rm "$temp_file"
    
    log_fix "Added missing function: $function_name"
}

# Fix missing generate_strong_password function
fix_generate_strong_password() {
    log_section "Fixing generate_strong_password Function"
    
    if ! check_function_exists "deploy-hauling-qr-ubuntu.sh" "generate_strong_password"; then
        local function_code='# Function to generate a strong random password
generate_strong_password() {
    local length="${1:-16}"
    local password=""
    
    # Ensure we have the required tools
    if command -v openssl &> /dev/null; then
        # Generate random password using OpenSSL
        password=$(openssl rand -base64 $((length * 2)) | tr -dc "a-zA-Z0-9!@#$%^&*()_+{}|:<>?~" | head -c "$length")
    else
        # Fallback to /dev/urandom
        password=$(cat /dev/urandom | tr -dc "a-zA-Z0-9!@#$%^&*()_+{}|:<>?~" | head -c "$length")
    fi
    
    echo "$password"
}'
        add_missing_function "deploy-hauling-qr-ubuntu.sh" "generate_strong_password" "$function_code"
    else
        log_info "generate_strong_password function already exists"
    fi
}

# Fix missing parse_shell_config function
fix_parse_shell_config() {
    log_section "Fixing parse_shell_config Function"
    
    if ! check_function_exists "deploy-hauling-qr-ubuntu.sh" "parse_shell_config"; then
        local function_code='# Function to parse shell-style configuration file
parse_shell_config() {
    local config_file="$1"
    
    if [[ ! -f "$config_file" ]]; then
        log_error "Configuration file not found: $config_file"
        return 1
    fi
    
    log_debug "Parsing shell configuration file: $config_file"
    
    # Source the configuration file
    source "$config_file"
    
    log_success "Shell configuration loaded from $config_file"
    return 0
}'
        add_missing_function "deploy-hauling-qr-ubuntu.sh" "parse_shell_config" "$function_code"
    else
        log_info "parse_shell_config function already exists"
    fi
}

# Fix missing validate_loaded_configuration function
fix_validate_loaded_configuration() {
    log_section "Fixing validate_loaded_configuration Function"
    
    if ! check_function_exists "deploy-hauling-qr-ubuntu.sh" "validate_loaded_configuration"; then
        local function_code='# Function to validate the loaded configuration
validate_loaded_configuration() {
    log_step "Validating loaded configuration"
    
    local errors=0
    local warnings=0
    local error_messages=""
    local warning_messages=""
    
    # Required fields
    if [[ -z "$DOMAIN_NAME" ]]; then
        error_messages+="- Domain name is required\\n"
        ((errors++))
    fi
    
    # Environment mode
    if [[ -z "$ENV_MODE" ]]; then
        ENV_MODE="production"
        warning_messages+="- Environment mode not specified, defaulting to production\\n"
        ((warnings++))
    elif [[ "$ENV_MODE" != "production" && "$ENV_MODE" != "staging" && "$ENV_MODE" != "development" ]]; then
        warning_messages+="- Invalid environment mode: $ENV_MODE, defaulting to production\\n"
        ENV_MODE="production"
        ((warnings++))
    fi
    
    # SSL mode
    if [[ -z "$SSL_MODE" ]]; then
        SSL_MODE="cloudflare"
        warning_messages+="- SSL mode not specified, defaulting to cloudflare\\n"
        ((warnings++))
    elif [[ "$SSL_MODE" == "cloudflare" && -z "$CLOUDFLARE_SSL_MODE" ]]; then
        CLOUDFLARE_SSL_MODE="full"
        warning_messages+="- Cloudflare SSL mode not specified, defaulting to full\\n"
        ((warnings++))
    fi
    
    # Display errors and warnings
    if [[ $errors -gt 0 ]]; then
        log_error "Configuration validation failed with $errors error(s):"
        echo -e "$error_messages"
        
        if [[ "$INTERACTIVE" == true && "$DRY_RUN" != true ]]; then
            read -p "Configuration has errors. Do you want to continue anyway? (y/n): " continue_choice
            if [[ "$continue_choice" != "y" ]]; then
                log_error "Deployment aborted due to configuration errors"
                exit 1
            fi
        else
            log_error "Deployment aborted due to configuration errors"
            exit 1
        fi
    fi
    
    if [[ $warnings -gt 0 ]]; then
        log_warning "Configuration validation completed with $warnings warning(s):"
        echo -e "$warning_messages"
        
        if [[ "$INTERACTIVE" == true && "$DRY_RUN" != true ]]; then
            read -p "Configuration has warnings. Do you want to continue? (y/n): " continue_choice
            if [[ "$continue_choice" != "y" ]]; then
                log_warning "Deployment aborted due to configuration warnings"
                exit 0
            fi
        fi
    fi
    
    if [[ $errors -eq 0 && $warnings -eq 0 ]]; then
        log_success "Configuration validation passed"
    fi
    
    return $errors
}'
        add_missing_function "deploy-hauling-qr-ubuntu.sh" "validate_loaded_configuration" "$function_code"
    else
        log_info "validate_loaded_configuration function already exists"
    fi
}

# Fix missing state management functions
fix_state_management() {
    log_section "Fixing State Management Functions"
    
    # Add init_deployment_state_management function
    if ! check_function_exists "deploy-hauling-qr-ubuntu.sh" "init_deployment_state_management"; then
        local function_code='# Initialize enhanced state management
init_deployment_state_management() {
    log_step "Initializing enhanced deployment state management"
    
    # Create state directories
    mkdir -p "$DEPLOYMENT_STATE_DIR" "$CHECKPOINT_DIR"
    
    # Generate unique deployment ID
    DEPLOYMENT_ID="deploy_$(date +%Y%m%d_%H%M%S)_$$"
    DEPLOYMENT_START_TIME=$(date +%s)
    
    # Initialize state file
    cat > "$STATE_FILE" << EOF
{
  "deployment_id": "$DEPLOYMENT_ID",
  "start_time": "$DEPLOYMENT_START_TIME",
  "start_date": "$(date)",
  "hostname": "$(hostname)",
  "user": "$(whoami)",
  "script_version": "$VERSION",
  "domain": "$DOMAIN_NAME",
  "environment": "$ENV_MODE",
  "current_phase": "initialization",
  "current_step": "init_state_management",
  "completed_phases": [],
  "completed_steps": [],
  "checkpoints": [],
  "last_checkpoint": null,
  "status": "in_progress",
  "error_count": 0,
  "warning_count": 0,
  "configuration": {
    "backup_enabled": $BACKUP_ENABLED,
    "monitoring_enabled": $MONITORING_ENABLED,
    "ssl_mode": "$SSL_MODE",
    "interactive": $INTERACTIVE
  }
}
EOF
    
    log_success "Deployment state management initialized (ID: $DEPLOYMENT_ID)"
    log_info "State directory: $DEPLOYMENT_STATE_DIR"
}'
        add_missing_function "deploy-hauling-qr-ubuntu.sh" "init_deployment_state_management" "$function_code"
    else
        log_info "init_deployment_state_management function already exists"
    fi
    
    # Add set_deployment_phase function
    if ! check_function_exists "deploy-hauling-qr-ubuntu.sh" "set_deployment_phase"; then
        local function_code='# Update current deployment phase
set_deployment_phase() {
    local phase="$1"
    local step="${2:-$phase}"
    
    CURRENT_PHASE="$phase"
    CURRENT_STEP="$step"
    
    log_debug "Setting deployment phase: $phase (step: $step)"
    
    # Update state file
    if [[ -f "$STATE_FILE" ]]; then
        local temp_file=$(mktemp)
        jq ".current_phase = \"$phase\" | 
            .current_step = \"$step\" | 
            .last_updated = \"$(date)\" |
            .last_updated_timestamp = $(date +%s)" "$STATE_FILE" > "$temp_file" && mv "$temp_file" "$STATE_FILE"
        
        # Check if we should create a checkpoint
        local phase_number=${DEPLOYMENT_PHASES[$phase]:-0}
        if [[ $((phase_number % CHECKPOINT_INTERVAL)) -eq 0 ]]; then
            create_deployment_checkpoint "$phase"
        fi
    fi
}'
        add_missing_function "deploy-hauling-qr-ubuntu.sh" "set_deployment_phase" "$function_code"
    else
        log_info "set_deployment_phase function already exists"
    fi
    
    # Add create_deployment_checkpoint function
    if ! check_function_exists "deploy-hauling-qr-ubuntu.sh" "create_deployment_checkpoint"; then
        local function_code='# Create deployment checkpoint
create_deployment_checkpoint() {
    local checkpoint_name="$1"
    local checkpoint_id="checkpoint_$(date +%Y%m%d_%H%M%S)"
    local checkpoint_file="$CHECKPOINT_DIR/$checkpoint_id.json"
    
    log_info "Creating deployment checkpoint: $checkpoint_name"
    
    # Create checkpoint directory if it doesn't exist
    mkdir -p "$CHECKPOINT_DIR"
    
    # Create checkpoint data
    cat > "$checkpoint_file" << EOF
{
  "checkpoint_id": "$checkpoint_id",
  "checkpoint_name": "$checkpoint_name",
  "deployment_id": "$DEPLOYMENT_ID",
  "timestamp": $(date +%s),
  "date": "$(date)",
  "phase": "$CURRENT_PHASE",
  "step": "$CURRENT_STEP",
  "system_state": {
    "hostname": "$(hostname)",
    "uptime": "$(uptime -p 2>/dev/null || echo 'unknown')"
  }
}
EOF
    
    # Update main state file with checkpoint reference
    if [[ -f "$STATE_FILE" ]]; then
        local temp_file=$(mktemp)
        jq ".checkpoints += [\"$checkpoint_id\"] | 
            .last_checkpoint = \"$checkpoint_id\" |
            .last_checkpoint_time = \"$(date)\"" "$STATE_FILE" > "$temp_file" && mv "$temp_file" "$STATE_FILE"
    fi
    
    LAST_CHECKPOINT="$checkpoint_id"
    log_success "Checkpoint created: $checkpoint_id"
}'
        add_missing_function "deploy-hauling-qr-ubuntu.sh" "create_deployment_checkpoint" "$function_code"
    else
        log_info "create_deployment_checkpoint function already exists"
    fi
    
    # Add resume_deployment_from_checkpoint function
    if ! check_function_exists "deploy-hauling-qr-ubuntu.sh" "resume_deployment_from_checkpoint"; then
        local function_code='# Resume deployment from last checkpoint
resume_deployment_from_checkpoint() {
    local checkpoint_id="$1"
    
    if [[ -z "$checkpoint_id" ]]; then
        # Find the latest checkpoint
        checkpoint_id=$(ls -t "$CHECKPOINT_DIR"/*.json 2>/dev/null | head -1 | basename | sed "s/.json$//")
    fi
    
    if [[ -z "$checkpoint_id" || ! -f "$CHECKPOINT_DIR/$checkpoint_id.json" ]]; then
        log_error "No valid checkpoint found for resumption"
        return 1
    fi
    
    log_section "Resuming Deployment from Checkpoint"
    log_info "Checkpoint ID: $checkpoint_id"
    
    # Load checkpoint data
    local checkpoint_file="$CHECKPOINT_DIR/$checkpoint_id.json"
    local checkpoint_phase=$(jq -r ".phase" "$checkpoint_file")
    local checkpoint_step=$(jq -r ".step" "$checkpoint_file")
    local checkpoint_deployment_id=$(jq -r ".deployment_id" "$checkpoint_file")
    
    log_info "Resuming from phase: $checkpoint_phase (step: $checkpoint_step)"
    log_info "Original deployment ID: $checkpoint_deployment_id"
    
    # Update current state
    CURRENT_PHASE="$checkpoint_phase"
    CURRENT_STEP="$checkpoint_step"
    LAST_CHECKPOINT="$checkpoint_id"
    
    # Update state file
    if [[ -f "$STATE_FILE" ]]; then
        local temp_file=$(mktemp)
        jq ".resumed_from_checkpoint = \"$checkpoint_id\" |
            .resume_time = \"$(date)\" |
            .current_phase = \"$checkpoint_phase\" |
            .current_step = \"$checkpoint_step\"" "$STATE_FILE" > "$temp_file" && mv "$temp_file" "$STATE_FILE"
    fi
    
    log_success "Deployment state resumed from checkpoint: $checkpoint_id"
    return 0
}'
        add_missing_function "deploy-hauling-qr-ubuntu.sh" "resume_deployment_from_checkpoint" "$function_code"
    else
        log_info "resume_deployment_from_checkpoint function already exists"
    fi
    
    # Add state management variables
    log_fix "Adding state management variables"
    local temp_file=$(mktemp)
    
    # Find the variables section
    local variables_line=$(grep -n "^# Default configuration values" deploy-hauling-qr-ubuntu.sh | cut -d: -f1)
    
    if [[ -z "$variables_line" ]]; then
        variables_line=$(grep -n "^CONFIG_FILE=" deploy-hauling-qr-ubuntu.sh | cut -d: -f1)
    fi
    
    if [[ -n "$variables_line" ]]; then
        head -n $variables_line deploy-hauling-qr-ubuntu.sh > "$temp_file"
        
        cat >> "$temp_file" << 'EOF'

# State management variables
DEPLOYMENT_STATE_DIR="/var/lib/hauling-deployment/state"
CHECKPOINT_DIR="$DEPLOYMENT_STATE_DIR/checkpoints"
STATE_FILE="$DEPLOYMENT_STATE_DIR/deployment-state.json"
CHECKPOINT_INTERVAL=5  # Save checkpoint every 5 major steps
CURRENT_PHASE=""
CURRENT_STEP=""
DEPLOYMENT_START_TIME=""
LAST_CHECKPOINT=""
DEPLOYMENT_ID=""

# Deployment phases
declare -A DEPLOYMENT_PHASES=(
    ["initialization"]="1"
    ["system_preparation"]="2"
    ["component_installation"]="3"
    ["configuration_setup"]="4"
    ["database_setup"]="5"
    ["application_deployment"]="6"
    ["ssl_configuration"]="7"
    ["service_configuration"]="8"
    ["security_setup"]="9"
    ["finalization"]="10"
)
EOF
        
        tail -n +$((variables_line + 1)) deploy-hauling-qr-ubuntu.sh >> "$temp_file"
        cp "$temp_file" deploy-hauling-qr-ubuntu.sh
        rm "$temp_file"
    else
        log_info "Could not find variables section, skipping variable addition"
    fi
}

# Main function
main() {
    log_header "Fixing deploy-hauling-qr-ubuntu.sh"
    
    # Make sure the script is executable
    chmod +x deploy-hauling-qr-ubuntu.sh
    
    # Fix missing functions
    fix_generate_strong_password
    fix_parse_shell_config
    fix_validate_loaded_configuration
    fix_state_management
    
    log_header "Fixes Applied"
    log_success "The deployment script has been fixed and is now ready for testing"
    log_info "Run ./final-deployment-test.sh again to verify the fixes"
}

# Run main function
main