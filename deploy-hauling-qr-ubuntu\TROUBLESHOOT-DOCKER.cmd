@echo off
REM Docker Ubuntu troubleshooting script
REM This will diagnose and fix common issues

echo === Docker Ubuntu 24.04 Troubleshooting ===
echo.

REM Check if Docker is running
echo Checking Docker status...
docker info >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker is not running!
    echo.
    echo Please:
    echo 1. Start Docker Desktop
    echo 2. Wait for the Docker icon to appear in system tray
    echo 3. Try again
    pause
    exit /b 1
)

echo Docker is running ✓
echo.

REM Check if image exists
echo Checking Ubuntu image...
docker image inspect ubuntu:24.04 >nul 2>&1
if errorlevel 1 (
    echo Ubuntu image not found, pulling...
    docker pull ubuntu:24.04
) else (
    echo Ubuntu image exists ✓
)

echo.

REM Remove existing container if it exists
echo Checking for existing container...
docker rm -f ubuntu-test-24 >nul 2>&1
if %errorlevel%==0 (
    echo Removed existing container ✓
)

echo.

REM Start fresh container
echo Starting fresh Ubuntu container...
echo.
echo Running: docker run -it --name ubuntu-test-24 -v %cd%:/workspace ubuntu:24.04 bash
echo.

docker run -it --name ubuntu-test-24 -v %cd%:/workspace ubuntu:24.04 bash

echo.
echo Container stopped.
echo To restart: docker start -ai ubuntu-test-24
pause