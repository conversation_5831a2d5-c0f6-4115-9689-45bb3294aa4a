# Hauling QR Trip System Deployment Configuration
# YAML format example

# Basic Configuration
domainName: truckhaul.top
sslMode: cloudflare
dbPassword: ""  # Leave empty for auto-generation
jwtSecret: ""   # Leave empty for auto-generation
adminUsername: admin
adminPassword: ""  # Leave empty for auto-generation
adminEmail: <EMAIL>
repoUrl: https://github.com/your-org/hauling-qr-trip-system.git
repoBranch: main
environment: production

# Optional Features
monitoringEnabled: true
backupEnabled: true
backupRetentionDays: 7

# Advanced Configuration
advanced:
  # Server Configuration
  nodeVersion: "18"
  postgresVersion: "15"
  appUser: hauling
  appDir: /var/www/hauling-qr-system
  
  # Performance Settings
  pm2Instances: max
  maxMemoryRestart: 2G
  nodeMaxOldSpace: 2048
  
  # Security Settings
  fail2banBantime: 3600
  fail2banMaxretry: 5
  ufwEnable: true
  
  # SSL Configuration
  sslCertPath: /etc/nginx/ssl/certificate.crt
  sslKeyPath: /etc/nginx/ssl/private.key
  sslCountry: US
  sslState: State
  sslCity: City
  sslOrg: Organization
  
  # Rate Limiting
  nginxRateLimitApi: 20r/s
  nginxRateLimitAuth: 5r/m
  nginxRateLimitGeneral: 10r/s
  
  # Database Optimization
  dbSharedBuffers: 256MB
  dbEffectiveCacheSize: 1GB
  dbWorkMem: 4MB
  dbMaxConnections: 100
  
  # Monitoring Configuration
  healthCheckInterval: "*/5 * * * *"
  performanceCheckInterval: "*/10 * * * *"
  reportGenerationTime: "0 6 * * *"
  
  # Backup Configuration
  fullBackupSchedule: "0 3 * * 0"
  backupCompression: true
  
  # Logging Configuration
  logLevel: info
  logRotationSize: 100M
  logRetentionDays: 52
  
  # Email Configuration
  smtpHost: smtp.gmail.com
  smtpPort: 587
  smtpUser: <EMAIL>
  smtpPassword: ""
  alertEmail: <EMAIL>

# Cloudflare Configuration
cloudflare:
  sslMode: full
  apiToken: ""
  zoneId: ""
  minifyHtml: true
  minifyCss: true
  minifyJs: true
  brotli: true
  cacheLevel: standard
  browserCacheTtl: 14400