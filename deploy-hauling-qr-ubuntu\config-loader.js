/**
 * Unified Configuration Loader for Hauling QR Trip System
 * Handles automatic environment detection, mode switching, and IP detection
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

/**
 * Get the primary local network IP address
 */
function getLocalNetworkIP() {
  const interfaces = os.networkInterfaces();
  const candidates = [];
  
  // Priority order for interface types
  const interfacePriority = {
    'Wi-Fi': 1,
    'WiFi': 1,
    'Wireless': 1,
    'Ethernet': 2,
    'Local Area Connection': 3,
    'en0': 4, // macOS WiFi
    'en1': 5, // macOS Ethernet
    'eth0': 6, // Linux Ethernet
    'wlan0': 7, // Linux WiFi
  };
  
  for (const [interfaceName, addresses] of Object.entries(interfaces)) {
    for (const addr of addresses) {
      // Skip internal/loopback addresses
      if (addr.internal || addr.family !== 'IPv4') {
        continue;
      }
      
      // Skip virtual machine interfaces
      if (interfaceName.toLowerCase().includes('vmware') ||
          interfaceName.toLowerCase().includes('virtualbox') ||
          interfaceName.toLowerCase().includes('hyper-v') ||
          interfaceName.toLowerCase().includes('docker') ||
          interfaceName.toLowerCase().includes('vethernet')) {
        continue;
      }
      
      // Determine priority
      let priority = 999; // Default low priority
      for (const [pattern, p] of Object.entries(interfacePriority)) {
        if (interfaceName.toLowerCase().includes(pattern.toLowerCase())) {
          priority = p;
          break;
        }
      }
      
      candidates.push({
        ip: addr.address,
        interface: interfaceName,
        priority: priority
      });
    }
  }
  
  // Sort by priority (lower number = higher priority)
  candidates.sort((a, b) => a.priority - b.priority);
  
  // Return the best candidate or fallback to localhost
  return candidates.length > 0 ? candidates[0].ip : '127.0.0.1';
}

/**
 * Load and process environment configuration
 */
function loadConfig() {
  // Load base environment variables from .env
  require('dotenv').config({ path: path.join(__dirname, '.env') });
  
  const config = {
    // Environment mode
    NODE_ENV: process.env.NODE_ENV || 'development',
    IS_DEVELOPMENT: (process.env.NODE_ENV || 'development') === 'development',
    IS_PRODUCTION: (process.env.NODE_ENV || 'development') === 'production',
    ENABLE_HTTPS: process.env.ENABLE_HTTPS === 'true',
    AUTO_DETECT_IP: process.env.AUTO_DETECT_IP !== 'false', // Default to true
    
    // Get IP address
    IP_ADDRESS: process.env.AUTO_DETECT_IP !== 'false' 
      ? getLocalNetworkIP() 
      : (process.env.MANUAL_IP || '127.0.0.1'),
    
    // Database
    DB_HOST: process.env.NODE_ENV === 'production' 
      ? (process.env.DB_HOST_PROD || process.env.DB_HOST) 
      : process.env.DB_HOST,
    DB_PORT: parseInt(process.env.DB_PORT) || 5432,
    DB_NAME: process.env.NODE_ENV === 'production' 
      ? (process.env.DB_NAME_PROD || process.env.DB_NAME) 
      : process.env.DB_NAME,
    DB_USER: process.env.NODE_ENV === 'production' 
      ? (process.env.DB_USER_PROD || process.env.DB_USER) 
      : process.env.DB_USER,
    DB_PASSWORD: process.env.DB_PASSWORD,
    DB_POOL_MAX: parseInt(process.env.DB_POOL_MAX) || 25,
    DB_POOL_MIN: parseInt(process.env.DB_POOL_MIN) || 5,
    
    // Server ports
    BACKEND_HTTP_PORT: parseInt(process.env.BACKEND_HTTP_PORT) || 5000,
    HTTPS_PORT: parseInt(process.env.HTTPS_PORT) || 5444,
    CLIENT_PORT: parseInt(process.env.PORT) || 3000, // React uses PORT env var
    
    // JWT
    JWT_SECRET: process.env.NODE_ENV === 'production' 
      ? (process.env.JWT_SECRET_PROD || process.env.JWT_SECRET) 
      : process.env.JWT_SECRET,
    JWT_EXPIRY: process.env.JWT_EXPIRY || '24h',
    
    // Rate limiting
    RATE_LIMIT_WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000,
    RATE_LIMIT_MAX_REQUESTS: process.env.NODE_ENV === 'production' 
      ? (parseInt(process.env.RATE_LIMIT_MAX_REQUESTS_PROD) || parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 5000)
      : (parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 1000),
    AUTH_RATE_LIMIT_WINDOW_MS: parseInt(process.env.AUTH_RATE_LIMIT_WINDOW_MS) || 900000,
    AUTH_RATE_LIMIT_MAX_REQUESTS: parseInt(process.env.AUTH_RATE_LIMIT_MAX_REQUESTS) || 50,
    
    // QR Code
    QR_CODE_SIZE: parseInt(process.env.QR_CODE_SIZE) || 200,
    QR_CODE_QUALITY: process.env.QR_CODE_QUALITY || 'H',
    
    // File uploads
    MAX_FILE_SIZE: parseInt(process.env.MAX_FILE_SIZE) || 5242880,
    UPLOAD_PATH: process.env.UPLOAD_PATH || './uploads',
    ALLOWED_FILE_TYPES: process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/gif,application/pdf',
    
    // Optional services
    REDIS_URL: process.env.REDIS_URL,
    SMTP_HOST: process.env.SMTP_HOST,
    SMTP_PORT: parseInt(process.env.SMTP_PORT) || 587,
    SMTP_USER: process.env.SMTP_USER,
    SMTP_PASS: process.env.SMTP_PASS,
    SENTRY_DSN: process.env.SENTRY_DSN,
    LOG_LEVEL: process.env.LOG_LEVEL || 'info'
  };
  
  // Auto-generate SSL paths based on environment
  if (config.NODE_ENV === 'production') {
    config.SSL_CERT_PATH = process.env.SSL_CERT_PATH_PROD;
    config.SSL_KEY_PATH = process.env.SSL_KEY_PATH_PROD;
    config.SSL_CA_PATH = process.env.SSL_CA_PATH_PROD;
  } else {
    config.SSL_CERT_PATH = process.env.SSL_CERT_PATH_DEV;
    config.SSL_KEY_PATH = process.env.SSL_KEY_PATH_DEV;
    config.SSL_CA_PATH = process.env.SSL_CA_PATH_DEV;
  }
  
  // Auto-generate URLs based on configuration
  const protocol = config.ENABLE_HTTPS ? 'https' : 'http';
  const wsProtocol = config.ENABLE_HTTPS ? 'wss' : 'ws';
  const serverPort = config.ENABLE_HTTPS ? config.HTTPS_PORT : config.BACKEND_HTTP_PORT;
  
  config.API_BASE_URL = `${protocol}://${config.IP_ADDRESS}:${serverPort}/api`;
  config.WS_URL = `${wsProtocol}://${config.IP_ADDRESS}:${serverPort}`;
  config.FRONTEND_URL = `${protocol}://${config.IP_ADDRESS}:${config.CLIENT_PORT}`;
  config.FRONTEND_URL_HTTP = `http://${config.IP_ADDRESS}:${config.CLIENT_PORT}`;
  config.FRONTEND_URL_HTTPS = `https://${config.IP_ADDRESS}:${config.CLIENT_PORT}`;
  
  // Development-specific settings
  if (config.NODE_ENV === 'development') {
    config.DEV_ENABLE_CORS_ALL = process.env.DEV_ENABLE_CORS_ALL === 'true';
    config.DEV_ENABLE_DETAILED_LOGS = process.env.DEV_ENABLE_DETAILED_LOGS === 'true';
    config.DEV_DISABLE_RATE_LIMITING = process.env.DEV_DISABLE_RATE_LIMITING === 'true';
  }
  
  return config;
}

/**
 * Generate client environment variables for React
 */
function generateClientEnv(config) {
  // CRITICAL FIX: Use localhost for development to avoid CORS issues
  const protocol = config.ENABLE_HTTPS ? 'https' : 'http';
  const wsProtocol = config.ENABLE_HTTPS ? 'wss' : 'ws';
  const serverPort = config.ENABLE_HTTPS ? config.HTTPS_PORT : config.BACKEND_HTTP_PORT;

  // CRITICAL FIX: Use network IP for both development and production to match backend
  // This ensures browser opens with network IP and WebSocket connects properly
  const hostname = config.IP_ADDRESS;

  const apiUrl = `${protocol}://${hostname}:${serverPort}/api`;
  const wsUrl = `${wsProtocol}://${hostname}:${serverPort}`;

  return {
    NODE_ENV: config.NODE_ENV,
    REACT_APP_API_URL: apiUrl,
    REACT_APP_WS_URL: wsUrl,
    REACT_APP_USE_HTTPS: config.ENABLE_HTTPS.toString(),
    REACT_APP_LOCAL_NETWORK_IP: config.IP_ADDRESS,
    REACT_APP_ENABLE_DEV_TOOLS: process.env.REACT_APP_ENABLE_DEV_TOOLS || 'false',
    REACT_APP_DEBUG_MODE: process.env.REACT_APP_DEBUG_MODE || 'false',
    REACT_APP_QR_CAMERA_FACING: process.env.REACT_APP_QR_CAMERA_FACING || 'environment',
    REACT_APP_QR_SCAN_DELAY: process.env.REACT_APP_QR_SCAN_DELAY || '500',
    GENERATE_SOURCEMAP: process.env.GENERATE_SOURCEMAP || 'true',
    PORT: config.CLIENT_PORT.toString(),
    HOST: '0.0.0.0',
    DANGEROUSLY_DISABLE_HOST_CHECK: 'true',
    HTTPS: config.ENABLE_HTTPS.toString()
  };
}

/**
 * Write client environment file
 * ELIMINATED: No more .env.local files - use only root .env
 */
function writeClientEnv(config) {
  // CRITICAL FIX: Instead of creating .env.local, update the root .env with client variables
  const envPath = path.join(__dirname, '.env');
  let envContent = fs.readFileSync(envPath, 'utf8');

  // Generate client environment variables
  const clientEnv = generateClientEnv(config);

  // Update or add React app variables in root .env
  const reactVars = {
    'REACT_APP_API_URL': clientEnv.REACT_APP_API_URL,
    'REACT_APP_WS_URL': clientEnv.REACT_APP_WS_URL,
    'REACT_APP_USE_HTTPS': clientEnv.REACT_APP_USE_HTTPS,
    'REACT_APP_LOCAL_NETWORK_IP': clientEnv.REACT_APP_LOCAL_NETWORK_IP
  };

  // Update existing variables or add new ones
  Object.entries(reactVars).forEach(([key, value]) => {
    const regex = new RegExp(`^${key}=.*$`, 'm');
    if (regex.test(envContent)) {
      envContent = envContent.replace(regex, `${key}=${value}`);
    } else {
      envContent += `\n${key}=${value}`;
    }
  });

  // Write back to root .env
  fs.writeFileSync(envPath, envContent);

  // Remove .env.local if it exists
  const clientEnvPath = path.join(__dirname, 'client', '.env.local');
  if (fs.existsSync(clientEnvPath)) {
    fs.unlinkSync(clientEnvPath);
    console.log(`🗑️  Removed: ${clientEnvPath}`);
  }

  console.log(`📝 Updated root .env with client variables`);
  console.log(`🔗 API URL: ${reactVars.REACT_APP_API_URL}`);
  console.log(`🔌 WebSocket URL: ${reactVars.REACT_APP_WS_URL}`);
}

/**
 * Display configuration summary
 */
function displayConfig(config) {
  console.log('\n🔧 HAULING QR TRIP SYSTEM - CONFIGURATION SUMMARY');
  console.log('='.repeat(60));
  console.log(`🌍 Environment: ${config.NODE_ENV}`);
  console.log(`🔒 HTTPS Enabled: ${config.ENABLE_HTTPS}`);
  console.log(`🌐 IP Address: ${config.IP_ADDRESS} (${config.AUTO_DETECT_IP ? 'auto-detected' : 'manual'})`);
  console.log(`🚀 Server: ${config.ENABLE_HTTPS ? 'https' : 'http'}://${config.IP_ADDRESS}:${config.ENABLE_HTTPS ? config.HTTPS_PORT : config.PORT}`);
  console.log(`💻 Client: ${config.ENABLE_HTTPS ? 'https' : 'http'}://${config.IP_ADDRESS}:${config.CLIENT_PORT}`);
  console.log(`📡 API: ${config.API_BASE_URL}`);
  console.log(`🔌 WebSocket: ${config.WS_URL}`);
  console.log(`🗄️  Database: ${config.DB_HOST}:${config.DB_PORT}/${config.DB_NAME}`);
  if (config.ENABLE_HTTPS) {
    console.log(`🔐 SSL Cert: ${config.SSL_CERT_PATH}`);
  }
  console.log('='.repeat(60));
  console.log('\n📋 ACCESS URLS:');
  console.log('='.repeat(40));
  console.log(`🌐 Frontend (React): https://${config.IP_ADDRESS}:${config.CLIENT_PORT}`);
  console.log(`📡 Backend API: ${config.API_BASE_URL}`);
  console.log(`🔍 Health Check: ${config.ENABLE_HTTPS ? 'https' : 'http'}://${config.IP_ADDRESS}:${config.ENABLE_HTTPS ? config.HTTPS_PORT : config.PORT}/health`);
  console.log('='.repeat(40));
  console.log('⚠️  Use Frontend URL for login/dashboard access!');
  console.log('✅ Backend URL is for API calls only.');
}

module.exports = {
  loadConfig,
  generateClientEnv,
  writeClientEnv,
  displayConfig,
  getLocalNetworkIP
};
