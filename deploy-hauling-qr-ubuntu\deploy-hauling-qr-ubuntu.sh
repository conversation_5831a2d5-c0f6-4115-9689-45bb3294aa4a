#!/bin/bash
#
# Hauling QR Trip Management System Deployment Script for Ubuntu
# Version: 2.0.0 - Enhanced Universal Compatibility
#
# This script automates the deployment of the Hauling QR Trip Management System
# on Ubuntu systems with universal compatibility for various environments including:
# - Ubuntu 18.04, 20.04, 22.04, 24.04 LTS versions
# - Docker containers (with and without systemd)
# - WSL1 and WSL2 environments
# - Standard VPS and bare metal installations
# - Both privileged and unprivileged execution contexts
#
# Usage: ./deploy-hauling-qr-ubuntu.sh [options]
#
# Options:
#   -c, --config FILE       Path to configuration file
#   -d, --domain DOMAIN     Domain name for the application
#   -e, --env MODE          Environment mode (production, staging, development)
#   -n, --non-interactive   Run in non-interactive mode (requires config file)
#   -q, --quiet             Minimal output
#   --dry-run               Validate configuration without making changes
#   --log-level LEVEL       Set log level (debug, info, warning, error)
#   --json-output           Output logs in JSON format for machine parsing
#   --rollback [BACKUP_ID]  Rollback to a specific backup (or latest if no ID provided)
#   --force-rollback [ID]   Force rollback without confirmation prompts
#   -h, --help              Show this help message
#
# Example:
#   ./deploy-hauling-qr-ubuntu.sh --domain example.com --env production
#   ./deploy-hauling-qr-ubuntu.sh --config deployment-config.conf
#   ./deploy-hauling-qr-ubuntu.sh --non-interactive --log-level debug --json-output
#   ./deploy-hauling-qr-ubuntu.sh --rollback
#   ./deploy-hauling-qr-ubuntu.sh --rollback 20250119_143022
#

# Record start time for metrics
DEPLOYMENT_START_TIME=$(date +%s)

# Script version
VERSION="2.0.0"

# Environment detection flags
INIT_SYSTEM=""
IS_DOCKER=false
IS_WSL=false
IS_SYSTEMD=false
HAS_SYSTEMCTL=false

# Default configuration values
CONFIG_FILE=""
DOMAIN_NAME="truckhaul.top"  # Default domain for this deployment
SSL_MODE="cloudflare"  # Using Cloudflare Full SSL mode
DB_PASSWORD=""
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="admin12345"
ADMIN_EMAIL=""
REPO_URL="https://github.com/mightybadz18/hauling-qr-trip-management.git"
REPO_BRANCH="main"
ENV_MODE="production"  # Options: production, staging, development
INTERACTIVE=true
VERBOSE=true
DRY_RUN=false
MONITORING_ENABLED=true
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=7
JWT_SECRET=""

# Logging setup
LOG_DIR="/var/log/hauling-deployment"
LOG_FILE="$LOG_DIR/deployment.log"
LOG_JSON_FILE="$LOG_DIR/deployment.json"
CONSOLE_OUTPUT=true
LOG_ROTATION_ENABLED=true
LOG_ROTATION_SIZE="10M"
LOG_RETENTION_COUNT=5

# Log levels
LOG_LEVEL_DEBUG=0
LOG_LEVEL_INFO=1
LOG_LEVEL_WARNING=2
LOG_LEVEL_ERROR=3
LOG_LEVEL_SUCCESS=4
CURRENT_LOG_LEVEL=$LOG_LEVEL_INFO

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Initialize JSON log file if it doesn't exist
if [[ ! -f "$LOG_JSON_FILE" ]]; then
    echo '{"logs":[]}' > "$LOG_JSON_FILE"
fi

# ============================================================================
# UNIVERSAL INIT SYSTEM DETECTION AND SERVICE MANAGEMENT
# ============================================================================

# Function to detect the environment and init system
detect_environment() {
    log_debug "Detecting environment and init system"

    # Detect Docker environment
    if [[ -f /.dockerenv ]] || grep -q 'docker\|lxc' /proc/1/cgroup 2>/dev/null; then
        IS_DOCKER=true
        log_info "Docker environment detected"
    fi

    # Detect WSL environment
    if grep -qEi "(Microsoft|WSL)" /proc/version 2>/dev/null; then
        IS_WSL=true
        log_info "WSL environment detected"
    fi

    # Detect systemd
    if [[ -d /run/systemd/system ]] && command -v systemctl &> /dev/null; then
        IS_SYSTEMD=true
        HAS_SYSTEMCTL=true
        INIT_SYSTEM="systemd"
        log_info "Systemd init system detected"
    elif command -v systemctl &> /dev/null; then
        HAS_SYSTEMCTL=true
        INIT_SYSTEM="systemd-fallback"
        log_warning "systemctl available but systemd may not be running as PID 1"
    elif command -v service &> /dev/null; then
        INIT_SYSTEM="sysv"
        log_info "SysV init system detected"
    elif command -v initctl &> /dev/null; then
        INIT_SYSTEM="upstart"
        log_info "Upstart init system detected"
    else
        INIT_SYSTEM="none"
        log_warning "No recognized init system detected - using process-based management"
    fi

    log_info "Environment summary: Docker=$IS_DOCKER, WSL=$IS_WSL, Init=$INIT_SYSTEM"

    # Set environment-specific configurations
    configure_environment_specific_settings
}

# Function to configure environment-specific settings
configure_environment_specific_settings() {
    log_debug "Configuring environment-specific settings"

    # Docker-specific configurations
    if [[ "$IS_DOCKER" == true ]]; then
        log_info "Applying Docker-specific configurations"

        # Disable unnecessary services in Docker
        export MONITORING_ENABLED=false
        export BACKUP_ENABLED=false

        # Use process-based service management
        export FORCE_PROCESS_MANAGEMENT=true

        # Adjust memory settings for containers
        export NODE_MAX_OLD_SPACE=1024
        export PM2_INSTANCES=1

        log_debug "Docker configurations applied"
    fi

    # WSL-specific configurations
    if [[ "$IS_WSL" == true ]]; then
        log_info "Applying WSL-specific configurations"

        # WSL has limited systemd support
        if [[ "$INIT_SYSTEM" == "none" ]]; then
            export FORCE_PROCESS_MANAGEMENT=true
        fi

        # Adjust for WSL networking quirks
        export WSL_NETWORKING=true

        log_debug "WSL configurations applied"
    fi

    # Non-systemd environment configurations
    if [[ "$INIT_SYSTEM" == "none" || "$INIT_SYSTEM" == "sysv" ]]; then
        log_info "Applying non-systemd configurations"

        # Use alternative service management
        export FORCE_PROCESS_MANAGEMENT=true

        # Disable systemd-specific features
        export SYSTEMD_FEATURES=false

        log_debug "Non-systemd configurations applied"
    fi
}

# Enhanced service management with environment awareness
service_start_enhanced() {
    local service_name=$1
    local fallback_command=${2:-""}

    # Use force process management if configured
    if [[ "$FORCE_PROCESS_MANAGEMENT" == true ]]; then
        case "$service_name" in
            "nginx")
                if nginx -t 2>/dev/null && nginx; then
                    log_debug "Nginx started via direct command"
                    return 0
                fi
                ;;
            "postgresql"|"postgres")
                if command -v pg_ctl >/dev/null 2>&1; then
                    local pg_data_dir=$(find /var/lib/postgresql -name "main" -type d 2>/dev/null | head -1)
                    if [[ -n "$pg_data_dir" ]] && sudo -u postgres pg_ctl -D "$pg_data_dir" start 2>/dev/null; then
                        log_debug "PostgreSQL started via pg_ctl"
                        return 0
                    fi
                fi
                ;;
        esac
    fi

    # Fall back to universal service management
    service_start "$service_name" "$fallback_command"
}

service_stop_enhanced() {
    local service_name=$1
    local fallback_command=${2:-""}

    # Use force process management if configured
    if [[ "$FORCE_PROCESS_MANAGEMENT" == true ]]; then
        case "$service_name" in
            "nginx")
                if nginx -s quit 2>/dev/null || nginx -s stop 2>/dev/null; then
                    log_debug "Nginx stopped via direct command"
                    return 0
                fi
                ;;
            "postgresql"|"postgres")
                if command -v pg_ctl >/dev/null 2>&1; then
                    local pg_data_dir=$(find /var/lib/postgresql -name "main" -type d 2>/dev/null | head -1)
                    if [[ -n "$pg_data_dir" ]] && sudo -u postgres pg_ctl -D "$pg_data_dir" stop 2>/dev/null; then
                        log_debug "PostgreSQL stopped via pg_ctl"
                        return 0
                    fi
                fi
                ;;
        esac
    fi

    # Fall back to universal service management
    service_stop "$service_name" "$fallback_command"
}

# Function to detect and handle Ubuntu version differences
handle_ubuntu_version_differences() {
    log_debug "Handling Ubuntu version-specific differences"

    if [[ -f /etc/os-release ]]; then
        local ubuntu_version=$(grep VERSION_ID /etc/os-release | cut -d'"' -f2)
        local major_version=$(echo "$ubuntu_version" | cut -d'.' -f1)

        case "$major_version" in
            "18")
                log_info "Applying Ubuntu 18.04 specific configurations"
                # Ubuntu 18.04 uses older package versions
                COMPONENT_REQUIRED_VERSIONS[nodejs]="16.0.0"  # Lower requirement
                COMPONENT_REQUIRED_VERSIONS[npm]="7.0.0"
                ;;
            "20")
                log_info "Applying Ubuntu 20.04 specific configurations"
                # Ubuntu 20.04 has good compatibility
                ;;
            "22")
                log_info "Applying Ubuntu 22.04 specific configurations"
                # Ubuntu 22.04 has newer systemd
                ;;
            "24")
                log_info "Applying Ubuntu 24.04 specific configurations"
                # Ubuntu 24.04 has latest features
                ;;
            *)
                log_warning "Unknown Ubuntu version: $ubuntu_version, using default configurations"
                ;;
        esac
    fi
}

# Universal service management functions
service_start() {
    local service_name=$1
    local fallback_command=${2:-""}

    log_debug "Starting service: $service_name"

    case "$INIT_SYSTEM" in
        "systemd")
            if systemctl start "$service_name" 2>/dev/null; then
                log_debug "Service $service_name started via systemctl"
                return 0
            fi
            ;;
        "systemd-fallback")
            if systemctl start "$service_name" 2>/dev/null; then
                log_debug "Service $service_name started via systemctl (fallback mode)"
                return 0
            fi
            ;;
        "sysv")
            if service "$service_name" start 2>/dev/null; then
                log_debug "Service $service_name started via service command"
                return 0
            fi
            ;;
        "upstart")
            if initctl start "$service_name" 2>/dev/null; then
                log_debug "Service $service_name started via initctl"
                return 0
            fi
            ;;
    esac

    # Fallback to manual command if provided
    if [[ -n "$fallback_command" ]]; then
        log_debug "Attempting fallback command for $service_name: $fallback_command"
        if eval "$fallback_command" 2>/dev/null; then
            log_debug "Service $service_name started via fallback command"
            return 0
        fi
    fi

    log_warning "Failed to start service: $service_name"
    return 1
}

service_stop() {
    local service_name=$1
    local fallback_command=${2:-""}

    log_debug "Stopping service: $service_name"

    case "$INIT_SYSTEM" in
        "systemd")
            if systemctl stop "$service_name" 2>/dev/null; then
                log_debug "Service $service_name stopped via systemctl"
                return 0
            fi
            ;;
        "systemd-fallback")
            if systemctl stop "$service_name" 2>/dev/null; then
                log_debug "Service $service_name stopped via systemctl (fallback mode)"
                return 0
            fi
            ;;
        "sysv")
            if service "$service_name" stop 2>/dev/null; then
                log_debug "Service $service_name stopped via service command"
                return 0
            fi
            ;;
        "upstart")
            if initctl stop "$service_name" 2>/dev/null; then
                log_debug "Service $service_name stopped via initctl"
                return 0
            fi
            ;;
    esac

    # Fallback to manual command if provided
    if [[ -n "$fallback_command" ]]; then
        log_debug "Attempting fallback command for $service_name: $fallback_command"
        if eval "$fallback_command" 2>/dev/null; then
            log_debug "Service $service_name stopped via fallback command"
            return 0
        fi
    fi

    log_warning "Failed to stop service: $service_name"
    return 1
}

service_enable() {
    local service_name=$1

    log_debug "Enabling service: $service_name"

    case "$INIT_SYSTEM" in
        "systemd")
            if systemctl enable "$service_name" 2>/dev/null; then
                log_debug "Service $service_name enabled via systemctl"
                return 0
            fi
            ;;
        "systemd-fallback")
            if systemctl enable "$service_name" 2>/dev/null; then
                log_debug "Service $service_name enabled via systemctl (fallback mode)"
                return 0
            fi
            ;;
        "sysv")
            if command -v update-rc.d &> /dev/null; then
                if update-rc.d "$service_name" enable 2>/dev/null; then
                    log_debug "Service $service_name enabled via update-rc.d"
                    return 0
                fi
            elif command -v chkconfig &> /dev/null; then
                if chkconfig "$service_name" on 2>/dev/null; then
                    log_debug "Service $service_name enabled via chkconfig"
                    return 0
                fi
            fi
            ;;
    esac

    log_debug "Service enable not supported or failed for: $service_name"
    return 0  # Don't fail deployment for enable issues
}

service_is_active() {
    local service_name=$1

    case "$INIT_SYSTEM" in
        "systemd"|"systemd-fallback")
            if systemctl is-active --quiet "$service_name" 2>/dev/null; then
                return 0
            fi
            ;;
        "sysv")
            if service "$service_name" status 2>/dev/null | grep -q "running\|active"; then
                return 0
            fi
            ;;
        "upstart")
            if initctl status "$service_name" 2>/dev/null | grep -q "start/running"; then
                return 0
            fi
            ;;
    esac

    # Fallback to process-based checking
    case "$service_name" in
        "nginx")
            if pgrep -x nginx >/dev/null 2>&1; then
                return 0
            fi
            ;;
        "postgresql"|"postgres")
            if pgrep -x postgres >/dev/null 2>&1 || pgrep -f "postgres.*main" >/dev/null 2>&1; then
                return 0
            fi
            ;;
        "fail2ban")
            if pgrep -x fail2ban-server >/dev/null 2>&1; then
                return 0
            fi
            ;;
    esac

    return 1
}

# Function to check if a port is in use (for service verification)
port_is_active() {
    local port=$1
    if command -v netstat &> /dev/null; then
        netstat -tuln 2>/dev/null | grep -q ":$port "
    elif command -v ss &> /dev/null; then
        ss -tuln 2>/dev/null | grep -q ":$port "
    else
        # Fallback using /proc/net/tcp
        local hex_port=$(printf "%04X" "$port")
        grep -q ":$hex_port " /proc/net/tcp 2>/dev/null
    fi
}

# ============================================================================
# ENHANCED NODE.JS ECOSYSTEM SETUP
# ============================================================================

# Function to install Node.js using multiple methods with fallbacks
install_nodejs_comprehensive() {
    local target_version=${1:-"18"}

    log_step "Installing Node.js (target version: $target_version)"

    # Method 1: Try NodeSource repository (most reliable)
    if install_nodejs_nodesource "$target_version"; then
        return 0
    fi

    # Method 2: Try snap package manager
    if command -v snap &> /dev/null && install_nodejs_snap; then
        return 0
    fi

    # Method 3: Try Node Version Manager (nvm)
    if install_nodejs_nvm "$target_version"; then
        return 0
    fi

    # Method 4: Try binary download
    if install_nodejs_binary "$target_version"; then
        return 0
    fi

    # Method 5: Try package manager (last resort)
    if install_nodejs_apt; then
        return 0
    fi

    log_error "All Node.js installation methods failed"
    return 1
}

# Method 1: NodeSource repository installation
install_nodejs_nodesource() {
    local target_version=$1

    log_info "Attempting Node.js installation via NodeSource repository"

    # Download and verify NodeSource setup script
    local setup_script="/tmp/nodesource_setup.sh"

    if curl -fsSL "https://deb.nodesource.com/setup_${target_version}.x" -o "$setup_script"; then
        log_debug "NodeSource setup script downloaded successfully"

        # Verify script is not empty and contains expected content
        if [[ -s "$setup_script" ]] && grep -q "nodesource" "$setup_script"; then
            log_debug "NodeSource setup script verified"

            # Run setup script
            if bash "$setup_script"; then
                log_debug "NodeSource repository setup completed"

                # Install Node.js
                if apt-get update && apt-get install -y nodejs; then
                    log_success "Node.js installed via NodeSource repository"
                    rm -f "$setup_script"
                    return 0
                fi
            fi
        else
            log_warning "NodeSource setup script verification failed"
        fi
        rm -f "$setup_script"
    else
        log_warning "Failed to download NodeSource setup script"
    fi

    return 1
}

# Method 2: Snap package installation
install_nodejs_snap() {
    log_info "Attempting Node.js installation via snap"

    if snap install node --classic 2>/dev/null; then
        log_success "Node.js installed via snap"
        return 0
    fi

    log_warning "Node.js snap installation failed"
    return 1
}

# Method 3: Node Version Manager (nvm) installation
install_nodejs_nvm() {
    local target_version=$1

    log_info "Attempting Node.js installation via nvm"

    # Install nvm
    local nvm_script="/tmp/nvm_install.sh"
    if curl -fsSL https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh -o "$nvm_script"; then
        if bash "$nvm_script"; then
            # Source nvm
            export NVM_DIR="$HOME/.nvm"
            [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

            # Install Node.js
            if nvm install "$target_version" && nvm use "$target_version"; then
                # Create symlinks for global access
                ln -sf "$NVM_DIR/versions/node/$(nvm version)/bin/node" /usr/local/bin/node
                ln -sf "$NVM_DIR/versions/node/$(nvm version)/bin/npm" /usr/local/bin/npm

                log_success "Node.js installed via nvm"
                rm -f "$nvm_script"
                return 0
            fi
        fi
        rm -f "$nvm_script"
    fi

    log_warning "Node.js nvm installation failed"
    return 1
}

# Method 4: Binary download installation
install_nodejs_binary() {
    local target_version=$1

    log_info "Attempting Node.js installation via binary download"

    # Detect architecture
    local arch
    case "$(uname -m)" in
        x86_64) arch="x64" ;;
        aarch64) arch="arm64" ;;
        armv7l) arch="armv7l" ;;
        *)
            log_warning "Unsupported architecture for binary installation: $(uname -m)"
            return 1
            ;;
    esac

    # Download and install Node.js binary
    local node_url="https://nodejs.org/dist/v${target_version}.0.0/node-v${target_version}.0.0-linux-${arch}.tar.xz"
    local temp_dir="/tmp/nodejs-binary"

    mkdir -p "$temp_dir"

    if curl -fsSL "$node_url" | tar -xJ -C "$temp_dir" --strip-components=1; then
        # Copy binaries to system location
        cp -r "$temp_dir"/* /usr/local/

        # Verify installation
        if command -v node &> /dev/null && command -v npm &> /dev/null; then
            log_success "Node.js installed via binary download"
            rm -rf "$temp_dir"
            return 0
        fi
    fi

    rm -rf "$temp_dir"
    log_warning "Node.js binary installation failed"
    return 1
}

# Method 5: APT package manager (fallback)
install_nodejs_apt() {
    log_info "Attempting Node.js installation via APT (fallback method)"

    if apt-get update && apt-get install -y nodejs npm; then
        log_success "Node.js installed via APT package manager"
        return 0
    fi

    log_warning "Node.js APT installation failed"
    return 1
}

# Function to verify and fix npm installation
verify_and_fix_npm() {
    log_step "Verifying NPM installation"

    # Check if npm is available
    if ! command -v npm &> /dev/null; then
        log_warning "NPM not found, attempting to install"

        # Try to install npm separately
        if apt-get install -y npm; then
            log_info "NPM installed via APT"
        elif command -v node &> /dev/null; then
            # Try to install npm via Node.js if available
            log_info "Attempting to install NPM via Node.js"
            curl -L https://www.npmjs.com/install.sh | sh
        else
            log_error "Cannot install NPM - Node.js not available"
            return 1
        fi
    fi

    # Verify npm works
    if npm --version &> /dev/null; then
        log_success "NPM is working correctly"

        # Update npm to latest version
        log_info "Updating NPM to latest version"
        npm install -g npm@latest 2>/dev/null || log_warning "NPM update failed, continuing with current version"

        return 0
    else
        log_error "NPM verification failed"
        return 1
    fi
}

# Function to install and verify PM2
install_and_verify_pm2() {
    log_step "Installing and verifying PM2"

    # Ensure npm is available
    if ! command -v npm &> /dev/null; then
        log_error "NPM is required for PM2 installation"
        return 1
    fi

    # Install PM2 globally
    log_info "Installing PM2 globally"
    if npm install -g pm2; then
        log_success "PM2 installed successfully"
    else
        log_error "PM2 installation failed"
        return 1
    fi

    # Verify PM2 installation
    if command -v pm2 &> /dev/null && pm2 --version &> /dev/null; then
        log_success "PM2 verification successful"
        return 0
    else
        log_error "PM2 verification failed"
        return 1
    fi
}

# Function to setup Node.js ecosystem with comprehensive error handling
setup_nodejs_ecosystem() {
    log_step "Setting up Node.js ecosystem"

    # Install Node.js
    if ! install_nodejs_comprehensive "18"; then
        log_error "Failed to install Node.js"
        return 1
    fi

    # Verify and fix npm
    if ! verify_and_fix_npm; then
        log_error "Failed to setup NPM"
        return 1
    fi

    # Install and verify PM2
    if ! install_and_verify_pm2; then
        log_error "Failed to setup PM2"
        return 1
    fi

    # Log final versions
    log_info "Node.js ecosystem setup completed:"
    log_info "  Node.js version: $(node --version 2>/dev/null || echo 'Not available')"
    log_info "  NPM version: $(npm --version 2>/dev/null || echo 'Not available')"
    log_info "  PM2 version: $(pm2 --version 2>/dev/null || echo 'Not available')"

    return 0
}

# ============================================================================
# ENHANCED ERROR RECOVERY AND RETRY MECHANISMS
# ============================================================================

# Function to retry a command with exponential backoff
retry_with_backoff() {
    local command="$1"
    local max_attempts=${2:-3}
    local base_delay=${3:-2}
    local max_delay=${4:-30}
    local description=${5:-"command"}

    local attempt=1
    local delay=$base_delay

    while [[ $attempt -le $max_attempts ]]; do
        log_debug "Attempting $description (attempt $attempt/$max_attempts)"

        if eval "$command"; then
            log_debug "$description succeeded on attempt $attempt"
            return 0
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            log_error "$description failed after $max_attempts attempts"
            return 1
        fi

        log_warning "$description failed on attempt $attempt, retrying in ${delay}s..."
        sleep "$delay"

        # Exponential backoff with jitter
        delay=$((delay * 2))
        if [[ $delay -gt $max_delay ]]; then
            delay=$max_delay
        fi

        # Add random jitter (0-25% of delay)
        local jitter=$((RANDOM % (delay / 4 + 1)))
        delay=$((delay + jitter))

        ((attempt++))
    done

    return 1
}

# Function to check network connectivity with multiple methods
check_network_connectivity() {
    local test_hosts=("*******" "*******" "google.com" "github.com")
    local connectivity_ok=false

    log_debug "Checking network connectivity"

    # Test with ping
    for host in "${test_hosts[@]}"; do
        if ping -c 1 -W 5 "$host" >/dev/null 2>&1; then
            log_debug "Network connectivity confirmed via ping to $host"
            connectivity_ok=true
            break
        fi
    done

    # Test with curl if ping failed
    if [[ "$connectivity_ok" == false ]]; then
        for host in "http://google.com" "http://github.com"; do
            if curl -s --connect-timeout 10 --max-time 15 "$host" >/dev/null 2>&1; then
                log_debug "Network connectivity confirmed via curl to $host"
                connectivity_ok=true
                break
            fi
        done
    fi

    # Test with wget if curl failed
    if [[ "$connectivity_ok" == false ]] && command -v wget >/dev/null 2>&1; then
        for host in "http://google.com" "http://github.com"; do
            if wget --timeout=10 --tries=1 -q --spider "$host" 2>/dev/null; then
                log_debug "Network connectivity confirmed via wget to $host"
                connectivity_ok=true
                break
            fi
        done
    fi

    if [[ "$connectivity_ok" == true ]]; then
        log_debug "Network connectivity check passed"
        return 0
    else
        log_warning "Network connectivity check failed"
        return 1
    fi
}

# Function to check and fix common system issues
check_and_fix_system_issues() {
    log_step "Checking and fixing common system issues"

    local issues_found=0
    local issues_fixed=0

    # Check disk space
    log_debug "Checking disk space"
    local root_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [[ $root_usage -gt 90 ]]; then
        log_warning "Root filesystem is ${root_usage}% full"
        ((issues_found++))

        # Try to clean up package cache
        if apt-get clean 2>/dev/null; then
            log_info "Cleaned package cache"
            ((issues_fixed++))
        fi

        # Try to clean up old logs
        if journalctl --vacuum-time=7d 2>/dev/null; then
            log_info "Cleaned old journal logs"
            ((issues_fixed++))
        fi
    fi

    # Check memory usage
    log_debug "Checking memory usage"
    local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [[ $mem_usage -gt 90 ]]; then
        log_warning "Memory usage is ${mem_usage}%"
        ((issues_found++))

        # Try to drop caches if safe to do so
        if [[ -w /proc/sys/vm/drop_caches ]]; then
            sync && echo 1 > /proc/sys/vm/drop_caches 2>/dev/null
            log_info "Dropped filesystem caches"
            ((issues_fixed++))
        fi
    fi

    # Check for broken packages
    log_debug "Checking for broken packages"
    if dpkg --audit 2>/dev/null | grep -q "broken"; then
        log_warning "Broken packages detected"
        ((issues_found++))

        if dpkg --configure -a 2>/dev/null && apt-get install -f -y 2>/dev/null; then
            log_info "Fixed broken packages"
            ((issues_fixed++))
        fi
    fi

    # Check for lock files
    log_debug "Checking for package manager lock files"
    local lock_files=("/var/lib/dpkg/lock" "/var/lib/dpkg/lock-frontend" "/var/cache/apt/archives/lock")
    for lock_file in "${lock_files[@]}"; do
        if [[ -f "$lock_file" ]]; then
            # Check if the process holding the lock is still running
            local lock_pid=$(lsof "$lock_file" 2>/dev/null | awk 'NR==2 {print $2}')
            if [[ -n "$lock_pid" ]] && ! kill -0 "$lock_pid" 2>/dev/null; then
                log_warning "Stale lock file detected: $lock_file"
                ((issues_found++))

                if rm -f "$lock_file" 2>/dev/null; then
                    log_info "Removed stale lock file: $lock_file"
                    ((issues_fixed++))
                fi
            fi
        fi
    done

    log_info "System check completed: $issues_found issues found, $issues_fixed fixed"
    return 0
}

# Function to validate prerequisites with detailed error messages
validate_prerequisites() {
    log_step "Validating deployment prerequisites"

    local validation_errors=0
    local validation_warnings=0

    # Check if running as root
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root or with sudo privileges"
        ((validation_errors++))
    fi

    # Check Ubuntu version
    if [[ -f /etc/os-release ]]; then
        local ubuntu_version=$(grep VERSION_ID /etc/os-release | cut -d'"' -f2)
        local major_version=$(echo "$ubuntu_version" | cut -d'.' -f1)

        if [[ $major_version -lt 18 ]]; then
            log_error "Ubuntu version $ubuntu_version is not supported. Minimum required: 18.04"
            ((validation_errors++))
        elif [[ $major_version -eq 18 || $major_version -eq 20 || $major_version -eq 22 || $major_version -eq 24 ]]; then
            log_info "Ubuntu version $ubuntu_version is supported"
        else
            log_warning "Ubuntu version $ubuntu_version is not tested. Supported versions: 18.04, 20.04, 22.04, 24.04"
            ((validation_warnings++))
        fi
    else
        log_warning "Cannot determine Ubuntu version"
        ((validation_warnings++))
    fi

    # Check network connectivity
    if ! check_network_connectivity; then
        log_error "Network connectivity is required for deployment"
        ((validation_errors++))
    fi

    # Check required commands
    local required_commands=("curl" "wget" "git" "apt-get")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            log_error "Required command not found: $cmd"
            ((validation_errors++))
        fi
    done

    # Check system resources
    local min_memory_mb=1024
    local available_memory=$(free -m | awk 'NR==2{print $7}')
    if [[ $available_memory -lt $min_memory_mb ]]; then
        log_warning "Available memory (${available_memory}MB) is below recommended minimum (${min_memory_mb}MB)"
        ((validation_warnings++))
    fi

    local min_disk_gb=5
    local available_disk=$(df / | awk 'NR==2 {print int($4/1024/1024)}')
    if [[ $available_disk -lt $min_disk_gb ]]; then
        log_error "Available disk space (${available_disk}GB) is below minimum requirement (${min_disk_gb}GB)"
        ((validation_errors++))
    fi

    # Summary
    if [[ $validation_errors -gt 0 ]]; then
        log_error "Prerequisites validation failed: $validation_errors errors, $validation_warnings warnings"
        log_error "Please fix the errors before proceeding with deployment"
        return 1
    elif [[ $validation_warnings -gt 0 ]]; then
        log_warning "Prerequisites validation completed with warnings: $validation_warnings warnings"
        return 0
    else
        log_success "Prerequisites validation passed"
        return 0
    fi
}

# ============================================================================
# ROBUST DIRECTORY MANAGEMENT AND PATH RESOLUTION
# ============================================================================

# Function to safely navigate to directory with validation
safe_cd() {
    local target_dir=$1
    local create_if_missing=${2:-false}

    # Resolve absolute path
    local abs_path
    if [[ "$target_dir" = /* ]]; then
        abs_path="$target_dir"
    else
        abs_path="$(pwd)/$target_dir"
    fi

    # Create directory if requested and it doesn't exist
    if [[ "$create_if_missing" == true ]] && [[ ! -d "$abs_path" ]]; then
        log_debug "Creating directory: $abs_path"
        if ! mkdir -p "$abs_path"; then
            log_error "Failed to create directory: $abs_path"
            return 1
        fi
    fi

    # Validate directory exists
    if [[ ! -d "$abs_path" ]]; then
        log_error "Directory does not exist: $abs_path"
        return 1
    fi

    # Validate directory is accessible
    if [[ ! -r "$abs_path" ]] || [[ ! -x "$abs_path" ]]; then
        log_error "Directory is not accessible: $abs_path"
        return 1
    fi

    # Change to directory
    if cd "$abs_path"; then
        log_debug "Successfully changed to directory: $abs_path"
        return 0
    else
        log_error "Failed to change to directory: $abs_path"
        return 1
    fi
}

# Function to handle repository cloning with conflict resolution
clone_repository_safe() {
    local repo_url=$1
    local target_dir=$2
    local branch=${3:-"main"}
    local backup_existing=${4:-true}

    log_step "Cloning repository safely"
    log_info "Repository: $repo_url"
    log_info "Target directory: $target_dir"
    log_info "Branch: $branch"

    # Check if target directory exists
    if [[ -d "$target_dir" ]]; then
        log_warning "Target directory already exists: $target_dir"

        # Check if it's a git repository
        if [[ -d "$target_dir/.git" ]]; then
            log_info "Existing git repository detected"

            # Check if it's the same repository
            if safe_cd "$target_dir"; then
                local current_remote=$(git remote get-url origin 2>/dev/null || echo "")
                if [[ "$current_remote" == "$repo_url" ]]; then
                    log_info "Same repository detected, updating instead of cloning"

                    # Fetch latest changes
                    if git fetch origin; then
                        # Reset to latest branch
                        if git reset --hard "origin/$branch"; then
                            log_success "Repository updated successfully"
                            return 0
                        else
                            log_warning "Failed to reset to latest branch, will re-clone"
                        fi
                    else
                        log_warning "Failed to fetch updates, will re-clone"
                    fi
                else
                    log_warning "Different repository detected (current: $current_remote)"
                fi
                cd - > /dev/null
            fi
        fi

        # Backup existing directory if requested
        if [[ "$backup_existing" == true ]]; then
            local backup_name="${target_dir}.backup.$(date +%Y%m%d_%H%M%S)"
            log_info "Backing up existing directory to: $backup_name"

            if mv "$target_dir" "$backup_name"; then
                log_success "Existing directory backed up successfully"
            else
                log_error "Failed to backup existing directory"
                return 1
            fi
        else
            # Remove existing directory
            log_info "Removing existing directory"
            if rm -rf "$target_dir"; then
                log_debug "Existing directory removed"
            else
                log_error "Failed to remove existing directory"
                return 1
            fi
        fi
    fi

    # Create parent directory if needed
    local parent_dir=$(dirname "$target_dir")
    if [[ ! -d "$parent_dir" ]]; then
        log_debug "Creating parent directory: $parent_dir"
        if ! mkdir -p "$parent_dir"; then
            log_error "Failed to create parent directory: $parent_dir"
            return 1
        fi
    fi

    # Clone repository
    log_info "Cloning repository..."
    if git clone --branch "$branch" --depth 1 "$repo_url" "$target_dir"; then
        log_success "Repository cloned successfully"

        # Verify clone
        if [[ -d "$target_dir" ]] && [[ -d "$target_dir/.git" ]]; then
            log_debug "Repository clone verified"
            return 0
        else
            log_error "Repository clone verification failed"
            return 1
        fi
    else
        log_error "Repository clone failed"
        return 1
    fi
}

# Function to create directory with proper permissions and ownership
create_directory_safe() {
    local dir_path=$1
    local owner=${2:-""}
    local permissions=${3:-"755"}

    log_debug "Creating directory safely: $dir_path"

    # Create directory with parents
    if mkdir -p "$dir_path"; then
        log_debug "Directory created: $dir_path"
    else
        log_error "Failed to create directory: $dir_path"
        return 1
    fi

    # Set permissions
    if chmod "$permissions" "$dir_path"; then
        log_debug "Permissions set to $permissions for: $dir_path"
    else
        log_warning "Failed to set permissions for: $dir_path"
    fi

    # Set ownership if specified
    if [[ -n "$owner" ]]; then
        if chown "$owner" "$dir_path"; then
            log_debug "Ownership set to $owner for: $dir_path"
        else
            log_warning "Failed to set ownership for: $dir_path"
        fi
    fi

    return 0
}

# Function to validate and create application directory structure
setup_application_directories() {
    local app_dir=${1:-"/var/www/hauling-qr-system"}
    local app_user=${2:-"hauling_app"}

    log_step "Setting up application directory structure"

    # Create main application directory
    if ! create_directory_safe "$app_dir" "$app_user:$app_user" "755"; then
        log_error "Failed to create main application directory"
        return 1
    fi

    # Create subdirectories
    local subdirs=("logs" "backups" "uploads" "ssl" "config")
    for subdir in "${subdirs[@]}"; do
        if ! create_directory_safe "$app_dir/$subdir" "$app_user:$app_user" "755"; then
            log_warning "Failed to create subdirectory: $subdir"
        fi
    done

    # Create log directories with appropriate permissions
    local log_dirs=("server/logs" "client/logs" "nginx/logs")
    for log_dir in "${log_dirs[@]}"; do
        if ! create_directory_safe "$app_dir/$log_dir" "$app_user:$app_user" "755"; then
            log_warning "Failed to create log directory: $log_dir"
        fi
    done

    log_success "Application directory structure created"
    return 0
}

# Function to validate working directory context
validate_working_directory() {
    local expected_dir=${1:-"/var/www/hauling-qr-system"}

    local current_dir=$(pwd)

    if [[ "$current_dir" != "$expected_dir" ]]; then
        log_warning "Working directory mismatch. Expected: $expected_dir, Current: $current_dir"

        # Attempt to change to expected directory
        if safe_cd "$expected_dir"; then
            log_info "Successfully changed to expected directory"
            return 0
        else
            log_error "Failed to change to expected directory"
            return 1
        fi
    fi

    log_debug "Working directory validated: $current_dir"
    return 0
}

# Rotate logs if enabled and necessary
if [[ "$LOG_ROTATION_ENABLED" == true && -f "$LOG_FILE" ]]; then
    log_size=$(du -b "$LOG_FILE" | cut -f1)
    max_size=$(echo "$LOG_ROTATION_SIZE" | sed 's/M/*1024*1024/' | bc)
    
    if [[ "$log_size" -gt "$max_size" ]]; then
        # Rotate logs
        for ((i=$LOG_RETENTION_COUNT-1; i>=0; i--)); do
            if [[ -f "$LOG_FILE.$i" ]]; then
                mv "$LOG_FILE.$i" "$LOG_FILE.$((i+1))"
            fi
        done
        
        # Move current log to .0
        mv "$LOG_FILE" "$LOG_FILE.0"
        
        # Create new log file
        touch "$LOG_FILE"
        
        # Remove oldest log if it exists
        if [[ -f "$LOG_FILE.$LOG_RETENTION_COUNT" ]]; then
            rm "$LOG_FILE.$LOG_RETENTION_COUNT"
        fi
    fi
fi

# Current step tracking for error handling
current_step="initialization"

# Error handling setup
set -o pipefail  # Ensure pipeline failures are captured

# Global error handler
trap 'handle_error_cicd $? $LINENO' ERR

# ==============================
# Logging Functions
# ==============================

# Helper function to add log entry to JSON log file
_log_to_json() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%dT%H:%M:%S.%3N%z')
    local context="$current_step"
    local hostname=$(hostname)
    local pid=$$
    
    # Create JSON log entry
    local json_entry="{\"timestamp\":\"$timestamp\",\"level\":\"$level\",\"message\":\"${message//\"/\\\"}\",\"context\":\"$context\",\"hostname\":\"$hostname\",\"pid\":$pid}"
    
    # Append to JSON log file using temporary file to avoid race conditions
    local temp_file=$(mktemp)
    jq ".logs += [$json_entry]" "$LOG_JSON_FILE" > "$temp_file" && mv "$temp_file" "$LOG_JSON_FILE"
}

log_debug() {
    local message="[DEBUG] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo "$message" >> "$LOG_FILE"
    [[ "$CONSOLE_OUTPUT" == true && $CURRENT_LOG_LEVEL -le $LOG_LEVEL_DEBUG ]] && echo -e "\033[0;90m$message\033[0m"
    _log_to_json "debug" "$1"
}

log_info() {
    local message="[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo "$message" >> "$LOG_FILE"
    [[ "$CONSOLE_OUTPUT" == true && $CURRENT_LOG_LEVEL -le $LOG_LEVEL_INFO ]] && echo -e "\033[0;34m$message\033[0m"
    _log_to_json "info" "$1"
}

log_warning() {
    local message="[WARNING] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo "$message" >> "$LOG_FILE"
    [[ "$CONSOLE_OUTPUT" == true && $CURRENT_LOG_LEVEL -le $LOG_LEVEL_WARNING ]] && echo -e "\033[0;33m$message\033[0m"
    _log_to_json "warning" "$1"
}

log_error() {
    local message="[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo "$message" >> "$LOG_FILE"
    [[ "$CONSOLE_OUTPUT" == true && $CURRENT_LOG_LEVEL -le $LOG_LEVEL_ERROR ]] && echo -e "\033[0;31m$message\033[0m"
    _log_to_json "error" "$1"
}

log_success() {
    local message="[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo "$message" >> "$LOG_FILE"
    [[ "$CONSOLE_OUTPUT" == true && $CURRENT_LOG_LEVEL -le $LOG_LEVEL_SUCCESS ]] && echo -e "\033[0;32m$message\033[0m"
    _log_to_json "success" "$1"
}

log_section() {
    local message="=== $1 ==="
    echo -e "\n$message" >> "$LOG_FILE"
    [[ "$CONSOLE_OUTPUT" == true ]] && echo -e "\n\033[1;36m$message\033[0m"
    _log_to_json "section" "$1"
    
    # Create a section marker in the log file for easier parsing
    echo -e "\n#---------- SECTION: $1 ($(date '+%Y-%m-%d %H:%M:%S')) ----------#\n" >> "$LOG_FILE"
}

log_step() {
    current_step=$(echo "$1" | tr ' ' '_' | tr '[:upper:]' '[:lower:]')
    local message="--- $1 ---"
    echo "$message" >> "$LOG_FILE"
    [[ "$CONSOLE_OUTPUT" == true ]] && echo -e "\033[0;36m$message\033[0m"
    _log_to_json "step" "$1"
    
    # Log additional context information for the step
    log_debug "Starting step: $1 (Memory: $(free -h | grep Mem | awk '{print $4}') available)"
    
    # Create a step marker in the log file for easier parsing
    echo -e "\n#---------- STEP: $1 ($(date '+%Y-%m-%d %H:%M:%S')) ----------#\n" >> "$LOG_FILE"
}

# Function to log command execution
log_command() {
    local command="$1"
    log_debug "Executing command: $command"
    
    # Execute the command and capture output
    local temp_file=$(mktemp)
    if eval "$command" > "$temp_file" 2>&1; then
        log_debug "Command succeeded: $command"
        cat "$temp_file" >> "$LOG_FILE"
        rm "$temp_file"
        return 0
    else
        local exit_code=$?
        log_error "Command failed with exit code $exit_code: $command"
        log_error "Command output: $(cat "$temp_file")"
        cat "$temp_file" >> "$LOG_FILE"
        rm "$temp_file"
        return $exit_code
    fi
}

# Function to log system metrics
log_system_metrics() {
    log_debug "System metrics - CPU: $(grep 'cpu ' /proc/stat | awk '{usage=($2+$4)*100/($2+$4+$5)} END {print usage "%"}'), Memory: $(free -h | grep Mem | awk '{print $3 "/" $2}'), Disk: $(df -h / | awk 'NR==2 {print $5}')"
}

# ==============================
# Error Handling Functions
# ==============================

handle_error() {
    local exit_code=$1
    local line_number=$2
    log_error "Error on line $line_number: Command failed with exit code $exit_code"
    
    # Attempt to diagnose common system issues
    diagnose_system_issues
    
    # Provide context-specific error messages and recovery suggestions
    case $current_step in
        "initialization")
            log_error "Initialization failed. This is likely due to script permission issues or corrupted script file."
            log_error "You can try manually running: chmod +x deploy-hauling-qr-ubuntu.sh"
            log_error "Or re-download the script and try again."
            ;;
        "system_update")
            log_error "System update failed. Check internet connectivity and try again."
            log_error "You can try manually running: apt-get update && apt-get upgrade -y"
            log_error "If you're behind a proxy, ensure your proxy settings are correctly configured."
            attempt_recovery "apt-get update -y" "Attempting to update package lists again..."
            ;;
        "nginx_config")
            log_error "Nginx configuration failed. Check for syntax errors in the configuration."
            log_error "You can try manually running: nginx -t"
            log_error "Common issues include: port conflicts, invalid server names, or syntax errors."
            # Attempt to validate the Nginx configuration
            nginx -t > /tmp/nginx-error.log 2>&1
            log_error "Nginx configuration test output: $(cat /tmp/nginx-error.log)"
            ;;
        "database_setup")
            log_error "Database setup failed. Check PostgreSQL installation and credentials."
            log_error "You can try manually running: sudo -u postgres psql -c '\\l' to list databases"
            log_error "Common issues include: PostgreSQL not running, insufficient permissions, or database already exists."
            # Check if PostgreSQL is running
            if ! service_is_active postgresql; then
                log_error "PostgreSQL is not running. Attempting to start..."
                attempt_recovery "service_start postgresql" "Starting PostgreSQL service..."
            fi
            ;;
        "repository_clone")
            log_error "Repository clone failed. Check repository URL and network connectivity."
            log_error "You can try manually running: git clone $REPO_URL"
            log_error "Common issues include: invalid repository URL, network connectivity issues, or Git not installed."
            # Check if Git is installed
            if ! command -v git &> /dev/null; then
                log_error "Git is not installed. Attempting to install..."
                attempt_recovery "apt-get install -y git" "Installing Git..."
            fi
            ;;
        "npm_install")
            log_error "NPM install failed. Check Node.js installation and package.json."
            log_error "You can try manually running: npm install"
            log_error "Common issues include: Node.js version mismatch, network connectivity issues, or package.json errors."
            # Check Node.js version
            log_error "Current Node.js version: $(node -v 2>/dev/null || echo 'Not installed')"
            log_error "Current NPM version: $(npm -v 2>/dev/null || echo 'Not installed')"
            ;;
        "frontend_build")
            log_error "Frontend build failed. Check for build errors in the frontend code."
            log_error "You can try manually running: cd client && npm run build"
            log_error "Common issues include: JavaScript syntax errors, missing dependencies, or insufficient memory."
            # Check available memory
            log_error "Available memory: $(free -h | grep Mem | awk '{print $4}')"
            ;;
        "ssl_setup")
            log_error "SSL setup failed. Check domain configuration and SSL settings."
            log_error "For Let's Encrypt, ensure the domain is properly configured with DNS."
            log_error "For Cloudflare, verify that the SSL mode is correctly set and the domain is properly configured."
            # Check if domain resolves to the correct IP
            server_ip=$(curl -s ifconfig.me)
            domain_ip=$(dig +short $DOMAIN_NAME 2>/dev/null || echo "Not resolved")
            log_error "Server IP: $server_ip, Domain IP: $domain_ip"
            if [[ "$domain_ip" != "$server_ip" && "$SSL_MODE" == "letsencrypt" ]]; then
                log_error "Domain does not resolve to this server's IP. Let's Encrypt will fail."
            fi
            ;;
        "environment_config")
            log_error "Environment configuration failed. Check file permissions and disk space."
            log_error "You can try manually creating the .env file with the required configuration."
            # Check disk space
            log_error "Available disk space: $(df -h / | awk 'NR==2 {print $4}')"
            ;;
        "pm2_setup")
            log_error "PM2 setup failed. Check Node.js and PM2 installation."
            log_error "You can try manually running: npm install -g pm2"
            log_error "Then: pm2 start server/server.js"
            # Check if PM2 is installed
            if ! command -v pm2 &> /dev/null; then
                log_error "PM2 is not installed. Attempting to install..."
                attempt_recovery "npm install -g pm2" "Installing PM2..."
            fi
            ;;
        "firewall_setup")
            log_error "Firewall setup failed. Check UFW installation and configuration."
            log_error "You can try manually running: ufw allow ssh && ufw allow http && ufw allow https && ufw --force enable"
            log_error "WARNING: Be careful with firewall commands to avoid locking yourself out of the server."
            ;;
        "user_creation")
            log_error "User creation failed. Check if the user already exists or if there are permission issues."
            log_error "You can try manually running: useradd -m -s /bin/bash hauling_app"
            # Check if user exists
            if id "hauling_app" &>/dev/null; then
                log_error "User 'hauling_app' already exists. This may not be an error."
            fi
            ;;
        "directory_setup")
            log_error "Directory setup failed. Check disk space and permissions."
            log_error "You can try manually running: mkdir -p /var/www/hauling-qr-system && chown hauling_app:hauling_app /var/www/hauling-qr-system"
            # Check disk space
            log_error "Available disk space: $(df -h / | awk 'NR==2 {print $4}')"
            ;;
        *)
            log_error "An unexpected error occurred during deployment in step: $current_step"
            log_error "Check the logs for more details and try running the specific commands manually."
            ;;
    esac
    
    # Generate comprehensive error report
    generate_error_report "$exit_code" "$line_number"
    
    # Attempt to save current state for potential rollback
    if [[ "$INTERACTIVE" == true ]]; then
        save_deployment_state
    fi
    
    # Exit with error code if not in interactive mode
    if [[ "$INTERACTIVE" != true ]]; then
        log_error "Exiting due to error in non-interactive mode. See error report at /tmp/hauling-deployment-error.log"
        exit $exit_code
    fi
    
    # In interactive mode, ask if user wants to continue
    echo
    echo "An error occurred during deployment. You have the following options:"
    echo "1. Continue deployment (may lead to partial installation)"
    echo "2. Exit deployment (current progress will be saved)"
    echo "3. Show detailed error report"
    
    while true; do
        read -p "Enter your choice (1-3): " error_choice
        case $error_choice in
            1)
                log_warning "Continuing deployment despite errors. Some features may not work correctly."
                break
                ;;
            2)
                log_warning "Exiting deployment. Run the script again to continue from this point."
                exit $exit_code
                ;;
            3)
                echo
                echo "=== DETAILED ERROR REPORT ==="
                cat /tmp/hauling-deployment-error.log
                echo "=== END OF ERROR REPORT ==="
                echo
                ;;
            *)
                echo "Invalid choice. Please enter 1, 2, or 3."
                ;;
        esac
    done
}

generate_error_report() {
    local exit_code=$1
    local line_number=$2
    
    # Create comprehensive error report file
    cat > /tmp/hauling-deployment-error.log << EOF
========================================================
Hauling QR Trip Management System Deployment Error Report
========================================================
Date: $(date)
Error on line $line_number with exit code $exit_code
Current step: $current_step
Domain: $DOMAIN_NAME
Environment: $ENV_MODE

========================================================
System Information:
========================================================
Hostname: $(hostname)
Kernel: $(uname -a)
Distribution: $(lsb_release -a 2>/dev/null || cat /etc/os-release)
CPU: $(grep "model name" /proc/cpuinfo | head -n1 | cut -d':' -f2 | xargs)
Memory: $(free -h | grep Mem | awk '{print $2}') total, $(free -h | grep Mem | awk '{print $4}') available
Disk: $(df -h / | awk 'NR==2 {print $2}') total, $(df -h / | awk 'NR==2 {print $4}') available

========================================================
Network Information:
========================================================
IP Address: $(hostname -I | awk '{print $1}')
External IP: $(curl -s ifconfig.me 2>/dev/null || echo "Unable to determine")
Domain Resolution: $(dig +short $DOMAIN_NAME 2>/dev/null || echo "Not resolved")
Connectivity: $(ping -c 1 -W 1 ******* > /dev/null 2>&1 && echo "Internet connection available" || echo "No internet connection")

========================================================
Service Status:
========================================================
Nginx: $(service_is_active nginx && echo "Active" || echo "Not active")
PostgreSQL: $(service_is_active postgresql && echo "Active" || echo "Not active")
UFW: $(service_is_active ufw && echo "Active" || echo "Not active")
Fail2Ban: $(service_is_active fail2ban && echo "Active" || echo "Not active")
PM2: $(command -v pm2 > /dev/null 2>&1 && echo "Installed" || echo "Not installed")

========================================================
Last 30 lines of deployment log:
========================================================
$(tail -n 30 "$LOG_FILE")

========================================================
Error Context:
========================================================
Command that failed: $(sed -n "${line_number}p" "$0" 2>/dev/null || echo "Unable to determine")
Previous command: $(sed -n "$((line_number-1))p" "$0" 2>/dev/null || echo "Unable to determine")
Next command: $(sed -n "$((line_number+1))p" "$0" 2>/dev/null || echo "Unable to determine")

========================================================
Troubleshooting Recommendations:
========================================================
1. Check the error message and context above for specific issues
2. Verify system requirements (Ubuntu 24.04, sufficient disk space and memory)
3. Ensure network connectivity and proper DNS configuration
4. Check that all required services are running
5. Review the deployment log for additional context
6. If using Cloudflare, verify your Cloudflare configuration

For assistance, please contact the system administrator or refer to the
documentation at https://github.com/your-org/hauling-qr-trip-system
EOF
    
    log_warning "Comprehensive error report saved to /tmp/hauling-deployment-error.log"
}

# Function to attempt recovery from common errors
attempt_recovery() {
    local recovery_command="$1"
    local recovery_message="$2"
    
    log_warning "$recovery_message"
    
    # Only attempt recovery in interactive mode
    if [[ "$INTERACTIVE" == true ]]; then
        read -p "Attempt automatic recovery? (y/n): " recovery_choice
        if [[ "$recovery_choice" == "y" ]]; then
            log_info "Attempting recovery with command: $recovery_command"
            if eval "$recovery_command"; then
                log_success "Recovery attempt successful"
                return 0
            else
                log_error "Recovery attempt failed"
                return 1
            fi
        else
            log_info "Automatic recovery skipped by user"
            return 1
        fi
    else
        log_warning "Automatic recovery not available in non-interactive mode"
        return 1
    fi
}

# Function to diagnose common system issues
diagnose_system_issues() {
    log_step "Diagnosing system issues"
    
    # Check disk space
    local disk_free=$(df -h / | awk 'NR==2 {print $5}' | tr -d '%')
    if [[ "$disk_free" -gt 90 ]]; then
        log_error "Critical: Disk space is low (${disk_free}% used). This may cause deployment failures."
    fi
    
    # Check memory
    local mem_free=$(free | grep Mem | awk '{print $4}')
    if [[ "$mem_free" -lt 524288 ]]; then  # Less than 512MB free
        log_error "Critical: Available memory is low ($(free -h | grep Mem | awk '{print $4}')). This may cause build failures."
    fi
    
    # Check network connectivity
    if ! ping -c 1 -W 1 ******* > /dev/null 2>&1; then
        log_error "Critical: No internet connectivity detected. Deployment will likely fail."
    fi
    
    # Check if required ports are available
    if command -v netstat > /dev/null 2>&1; then
        if netstat -tuln | grep -q ':80\s'; then
            log_error "Warning: Port 80 is already in use. This may conflict with Nginx."
        fi
        if netstat -tuln | grep -q ':443\s'; then
            log_error "Warning: Port 443 is already in use. This may conflict with Nginx."
        fi
        if netstat -tuln | grep -q ':5000\s'; then
            log_error "Warning: Port 5000 is already in use. This may conflict with the Node.js application."
        fi
        if netstat -tuln | grep -q ':5432\s'; then
            log_error "Warning: Port 5432 is already in use. This may conflict with PostgreSQL."
        fi
    fi
}

# ==============================
# Configuration Backup Functions
# ==============================

# Global backup directory with timestamp
BACKUP_BASE_DIR="/var/lib/hauling-deployment/backups"
BACKUP_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_BASE_DIR/$BACKUP_TIMESTAMP"

# Initialize backup system
init_backup_system() {
    log_step "Initializing configuration backup system"
    
    # Create backup directory structure
    mkdir -p "$BACKUP_DIR"/{nginx,postgresql,ssl,environment,firewall,pm2,system}
    
    # Create backup metadata file
    cat > "$BACKUP_DIR/backup-metadata.json" << EOF
{
  "timestamp": "$(date +%s)",
  "date": "$(date)",
  "backup_id": "$BACKUP_TIMESTAMP",
  "domain": "$DOMAIN_NAME",
  "environment": "$ENV_MODE",
  "script_version": "$VERSION",
  "hostname": "$(hostname)",
  "backup_files": []
}
EOF
    
    log_success "Backup system initialized at $BACKUP_DIR"
}

# Function to backup a single configuration file
backup_config_file() {
    local source_file="$1"
    local backup_category="$2"
    local description="$3"
    
    if [[ ! -f "$source_file" ]]; then
        log_debug "Skipping backup of $source_file - file does not exist"
        return 0
    fi
    
    # Create category directory if it doesn't exist
    mkdir -p "$BACKUP_DIR/$backup_category"
    
    # Generate backup filename with original path preserved
    local backup_filename=$(echo "$source_file" | sed 's|/|_|g' | sed 's|^_||')
    local backup_path="$BACKUP_DIR/$backup_category/$backup_filename"
    
    # Copy the file with metadata preservation
    if cp -p "$source_file" "$backup_path"; then
        log_info "Backed up: $source_file -> $backup_path"
        
        # Add to backup metadata
        local temp_file=$(mktemp)
        jq ".backup_files += [{
            \"source\": \"$source_file\",
            \"backup_path\": \"$backup_path\",
            \"category\": \"$backup_category\",
            \"description\": \"$description\",
            \"timestamp\": \"$(date)\",
            \"size\": $(stat -c%s "$source_file" 2>/dev/null || echo 0),
            \"permissions\": \"$(stat -c%a "$source_file" 2>/dev/null || echo 'unknown')\",
            \"owner\": \"$(stat -c%U:%G "$source_file" 2>/dev/null || echo 'unknown')\"
        }]" "$BACKUP_DIR/backup-metadata.json" > "$temp_file" && mv "$temp_file" "$BACKUP_DIR/backup-metadata.json"
        
        return 0
    else
        log_error "Failed to backup $source_file"
        return 1
    fi
}

# Function to backup Nginx configurations
backup_nginx_configs() {
    log_step "Backing up Nginx configurations"
    
    # Main nginx configuration
    backup_config_file "/etc/nginx/nginx.conf" "nginx" "Main Nginx configuration"
    
    # Sites available
    if [[ -d "/etc/nginx/sites-available" ]]; then
        for site_config in /etc/nginx/sites-available/*; do
            if [[ -f "$site_config" ]]; then
                local site_name=$(basename "$site_config")
                backup_config_file "$site_config" "nginx" "Site configuration: $site_name"
            fi
        done
    fi
    
    # Sites enabled
    if [[ -d "/etc/nginx/sites-enabled" ]]; then
        for enabled_site in /etc/nginx/sites-enabled/*; do
            if [[ -f "$enabled_site" ]]; then
                local site_name=$(basename "$enabled_site")
                backup_config_file "$enabled_site" "nginx" "Enabled site: $site_name"
            fi
        done
    fi
    
    # Nginx modules configuration
    if [[ -d "/etc/nginx/modules-enabled" ]]; then
        for module_config in /etc/nginx/modules-enabled/*; do
            if [[ -f "$module_config" ]]; then
                local module_name=$(basename "$module_config")
                backup_config_file "$module_config" "nginx" "Module configuration: $module_name"
            fi
        done
    fi
    
    log_success "Nginx configurations backed up"
}

# Function to backup PostgreSQL configurations
backup_postgresql_configs() {
    log_step "Backing up PostgreSQL configurations"

    local backup_success=false
    local pg_configs_found=0

    # Method 1: Try to detect PostgreSQL version via SQL
    local pg_version=""
    if command -v sudo >/dev/null 2>&1 && id postgres >/dev/null 2>&1; then
        pg_version=$(sudo -u postgres psql -t -c "SELECT version();" 2>/dev/null | grep -oP 'PostgreSQL \K[0-9]+' | head -1)
    fi

    # Method 2: Try to detect via package manager
    if [[ -z "$pg_version" ]]; then
        pg_version=$(dpkg -l | grep postgresql-[0-9] | head -1 | awk '{print $2}' | grep -oP '[0-9]+')
    fi

    # Method 3: Try to find config directories directly
    local pg_config_dirs=()
    if [[ -n "$pg_version" ]]; then
        pg_config_dirs+=("/etc/postgresql/$pg_version/main")
    fi

    # Add common PostgreSQL config locations
    pg_config_dirs+=(
        "/etc/postgresql/*/main"
        "/var/lib/postgresql/*/main"
        "/usr/local/pgsql/data"
        "/opt/postgresql/*/data"
    )

    # Try each potential config directory
    for config_pattern in "${pg_config_dirs[@]}"; do
        # Handle glob patterns
        for config_dir in $config_pattern; do
            if [[ -d "$config_dir" ]]; then
                log_info "Found PostgreSQL config directory: $config_dir"

                # Backup main configuration files
                local config_files=(
                    "postgresql.conf:Main PostgreSQL configuration"
                    "pg_hba.conf:PostgreSQL host-based authentication"
                    "pg_ident.conf:PostgreSQL user name mapping"
                    "environment:PostgreSQL environment variables"
                    "postgresql.auto.conf:PostgreSQL auto configuration"
                )

                for config_entry in "${config_files[@]}"; do
                    local config_file="${config_entry%%:*}"
                    local config_desc="${config_entry##*:}"
                    local full_path="$config_dir/$config_file"

                    if backup_config_file "$full_path" "postgresql" "$config_desc"; then
                        ((pg_configs_found++))
                        backup_success=true
                    fi
                done

                # Only process the first valid directory found
                break 2
            fi
        done
    done

    # Final status
    if [[ "$backup_success" == true ]]; then
        log_success "PostgreSQL configurations backed up ($pg_configs_found files found)"
    else
        log_warning "No PostgreSQL configuration files found to backup"
        log_info "This is normal if PostgreSQL is not installed or uses non-standard locations"
    fi
}

# Function to backup SSL certificates and configurations
backup_ssl_configs() {
    log_step "Backing up SSL certificates and configurations"
    
    # Nginx SSL directory
    if [[ -d "/etc/nginx/ssl" ]]; then
        for ssl_file in /etc/nginx/ssl/*; do
            if [[ -f "$ssl_file" ]]; then
                local ssl_filename=$(basename "$ssl_file")
                backup_config_file "$ssl_file" "ssl" "SSL file: $ssl_filename"
            fi
        done
    fi
    
    # Let's Encrypt certificates
    if [[ -d "/etc/letsencrypt" ]]; then
        # Archive directory contains the actual certificates
        if [[ -d "/etc/letsencrypt/archive" ]]; then
            for domain_dir in /etc/letsencrypt/archive/*; do
                if [[ -d "$domain_dir" ]]; then
                    local domain_name=$(basename "$domain_dir")
                    for cert_file in "$domain_dir"/*; do
                        if [[ -f "$cert_file" ]]; then
                            local cert_filename=$(basename "$cert_file")
                            backup_config_file "$cert_file" "ssl" "Let's Encrypt certificate: $domain_name/$cert_filename"
                        fi
                    done
                fi
            done
        fi
        
        # Renewal configuration
        if [[ -d "/etc/letsencrypt/renewal" ]]; then
            for renewal_config in /etc/letsencrypt/renewal/*; do
                if [[ -f "$renewal_config" ]]; then
                    local config_name=$(basename "$renewal_config")
                    backup_config_file "$renewal_config" "ssl" "Let's Encrypt renewal config: $config_name"
                fi
            done
        fi
    fi
    
    log_success "SSL configurations backed up"
}

# Function to backup firewall configurations
backup_firewall_configs() {
    log_step "Backing up firewall configurations"
    
    # UFW configuration
    if [[ -d "/etc/ufw" ]]; then
        backup_config_file "/etc/ufw/ufw.conf" "firewall" "UFW main configuration"
        backup_config_file "/etc/ufw/before.rules" "firewall" "UFW before rules"
        backup_config_file "/etc/ufw/after.rules" "firewall" "UFW after rules"
        backup_config_file "/etc/ufw/user.rules" "firewall" "UFW user rules"
        backup_config_file "/etc/ufw/user6.rules" "firewall" "UFW IPv6 user rules"
        
        # Application profiles
        if [[ -d "/etc/ufw/applications.d" ]]; then
            for app_profile in /etc/ufw/applications.d/*; do
                if [[ -f "$app_profile" ]]; then
                    local profile_name=$(basename "$app_profile")
                    backup_config_file "$app_profile" "firewall" "UFW application profile: $profile_name"
                fi
            done
        fi
    fi
    
    # Fail2Ban configuration
    if [[ -d "/etc/fail2ban" ]]; then
        backup_config_file "/etc/fail2ban/fail2ban.conf" "firewall" "Fail2Ban main configuration"
        backup_config_file "/etc/fail2ban/jail.conf" "firewall" "Fail2Ban jail configuration"
        
        # Local configurations (these override defaults)
        backup_config_file "/etc/fail2ban/fail2ban.local" "firewall" "Fail2Ban local configuration"
        backup_config_file "/etc/fail2ban/jail.local" "firewall" "Fail2Ban local jail configuration"
        
        # Custom jail configurations
        if [[ -d "/etc/fail2ban/jail.d" ]]; then
            for jail_config in /etc/fail2ban/jail.d/*; do
                if [[ -f "$jail_config" ]]; then
                    local jail_name=$(basename "$jail_config")
                    backup_config_file "$jail_config" "firewall" "Fail2Ban jail config: $jail_name"
                fi
            done
        fi
        
        # Filter configurations
        if [[ -d "/etc/fail2ban/filter.d" ]]; then
            for filter_config in /etc/fail2ban/filter.d/*.local; do
                if [[ -f "$filter_config" ]]; then
                    local filter_name=$(basename "$filter_config")
                    backup_config_file "$filter_config" "firewall" "Fail2Ban filter: $filter_name"
                fi
            done
        fi
    fi
    
    log_success "Firewall configurations backed up"
}

# Function to backup environment and application configurations
backup_environment_configs() {
    log_step "Backing up environment configurations"
    
    # Application environment files
    backup_config_file ".env" "environment" "Main application environment file"
    backup_config_file "client/.env" "environment" "Client environment file"
    backup_config_file "server/.env" "environment" "Server environment file"
    backup_config_file "database/.env" "environment" "Database environment file"
    
    # Application configuration files
    backup_config_file "package.json" "environment" "Root package.json"
    backup_config_file "client/package.json" "environment" "Client package.json"
    backup_config_file "server/package.json" "environment" "Server package.json"
    
    # PM2 ecosystem file if exists
    backup_config_file "ecosystem.config.js" "pm2" "PM2 ecosystem configuration"
    backup_config_file "pm2.config.js" "pm2" "PM2 configuration"
    
    log_success "Environment configurations backed up"
}

# Function to backup system configurations
backup_system_configs() {
    log_step "Backing up system configurations"
    
    # Systemd service files for our application
    if [[ -f "/etc/systemd/system/hauling-qr-system.service" ]]; then
        backup_config_file "/etc/systemd/system/hauling-qr-system.service" "system" "Hauling QR System service"
    fi
    
    # Cron jobs
    if crontab -l > /dev/null 2>&1; then
        crontab -l > "$BACKUP_DIR/system/crontab.backup"
        log_info "Backed up: crontab -> $BACKUP_DIR/system/crontab.backup"
    fi
    
    # Logrotate configurations
    backup_config_file "/etc/logrotate.d/hauling-qr-system" "system" "Logrotate configuration"
    
    log_success "System configurations backed up"
}

# Function to verify backup integrity
verify_backup_integrity() {
    log_step "Verifying backup integrity"
    
    local backup_count=0
    local failed_verifications=0
    
    # Read backup metadata and verify each file
    if [[ -f "$BACKUP_DIR/backup-metadata.json" ]]; then
        while IFS= read -r backup_entry; do
            local source_file=$(echo "$backup_entry" | jq -r '.source')
            local backup_path=$(echo "$backup_entry" | jq -r '.backup_path')
            local expected_size=$(echo "$backup_entry" | jq -r '.size')
            
            if [[ -f "$backup_path" ]]; then
                local actual_size=$(stat -c%s "$backup_path" 2>/dev/null || echo 0)
                if [[ "$actual_size" == "$expected_size" ]]; then
                    log_debug "Backup verified: $backup_path (size: $actual_size bytes)"
                    ((backup_count++))
                else
                    log_error "Backup size mismatch: $backup_path (expected: $expected_size, actual: $actual_size)"
                    ((failed_verifications++))
                fi
            else
                log_error "Backup file missing: $backup_path"
                ((failed_verifications++))
            fi
        done < <(jq -c '.backup_files[]' "$BACKUP_DIR/backup-metadata.json" 2>/dev/null || echo "")
    fi
    
    if [[ $failed_verifications -eq 0 ]]; then
        log_success "Backup integrity verified: $backup_count files backed up successfully"
        
        # Update backup metadata with verification status
        local temp_file=$(mktemp)
        jq ".verification = {
            \"verified\": true,
            \"verification_time\": \"$(date)\",
            \"total_files\": $backup_count,
            \"failed_verifications\": $failed_verifications
        }" "$BACKUP_DIR/backup-metadata.json" > "$temp_file" && mv "$temp_file" "$BACKUP_DIR/backup-metadata.json"
        
        return 0
    else
        log_error "Backup verification failed: $failed_verifications files failed verification"
        
        # Update backup metadata with verification status
        local temp_file=$(mktemp)
        jq ".verification = {
            \"verified\": false,
            \"verification_time\": \"$(date)\",
            \"total_files\": $backup_count,
            \"failed_verifications\": $failed_verifications
        }" "$BACKUP_DIR/backup-metadata.json" > "$temp_file" && mv "$temp_file" "$BACKUP_DIR/backup-metadata.json"
        
        return 1
    fi
}

# Function to cleanup old backups
cleanup_old_backups() {
    log_step "Cleaning up old backups"
    
    if [[ ! -d "$BACKUP_BASE_DIR" ]]; then
        log_debug "No backup directory found, skipping cleanup"
        return 0
    fi
    
    local retention_days=${BACKUP_RETENTION_DAYS:-7}
    local cleanup_count=0
    
    # Find and remove backup directories older than retention period
    find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -name "20*" -mtime +$retention_days | while read -r old_backup_dir; do
        local backup_id=$(basename "$old_backup_dir")
        log_info "Removing old backup: $backup_id (older than $retention_days days)"
        
        if rm -rf "$old_backup_dir"; then
            ((cleanup_count++))
            log_debug "Successfully removed old backup: $old_backup_dir"
        else
            log_error "Failed to remove old backup: $old_backup_dir"
        fi
    done
    
    # Count remaining backups
    local remaining_backups=$(find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -name "20*" | wc -l)
    
    log_success "Backup cleanup completed. Remaining backups: $remaining_backups"
}

# Function to list available backups
list_available_backups() {
    log_step "Listing available backups"
    
    if [[ ! -d "$BACKUP_BASE_DIR" ]]; then
        log_info "No backup directory found"
        return 0
    fi
    
    local backup_count=0
    
    echo
    echo "Available Configuration Backups:"
    echo "================================="
    
    # List backup directories sorted by date (newest first)
    find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -name "20*" | sort -r | while read -r backup_dir; do
        local backup_id=$(basename "$backup_dir")
        local backup_date=$(date -d "${backup_id:0:8} ${backup_id:9:2}:${backup_id:11:2}:${backup_id:13:2}" '+%Y-%m-%d %H:%M:%S' 2>/dev/null || echo "Unknown date")
        local backup_size=$(du -sh "$backup_dir" 2>/dev/null | cut -f1 || echo "Unknown size")
        
        # Read backup metadata if available
        local metadata_file="$backup_dir/backup-metadata.json"
        if [[ -f "$metadata_file" ]]; then
            local domain=$(jq -r '.domain // "Unknown"' "$metadata_file" 2>/dev/null)
            local environment=$(jq -r '.environment // "Unknown"' "$metadata_file" 2>/dev/null)
            local file_count=$(jq -r '.backup_files | length' "$metadata_file" 2>/dev/null || echo "Unknown")
            local verified=$(jq -r '.verification.verified // false' "$metadata_file" 2>/dev/null)
            
            echo "  Backup ID: $backup_id"
            echo "  Date: $backup_date"
            echo "  Domain: $domain"
            echo "  Environment: $environment"
            echo "  Files: $file_count"
            echo "  Size: $backup_size"
            echo "  Verified: $verified"
            echo "  Path: $backup_dir"
            echo "  ---"
        else
            echo "  Backup ID: $backup_id"
            echo "  Date: $backup_date"
            echo "  Size: $backup_size"
            echo "  Path: $backup_dir"
            echo "  Metadata: Not available"
            echo "  ---"
        fi
        
        ((backup_count++))
    done
    
    echo "Total backups: $backup_count"
    echo
    
    # Show latest backup symlink if it exists
    if [[ -L "$BACKUP_BASE_DIR/latest" ]]; then
        local latest_target=$(readlink "$BACKUP_BASE_DIR/latest")
        echo "Latest backup points to: $(basename "$latest_target")"
    fi
    
    log_success "Listed $backup_count available backups"
}

# Function to show detailed backup information
show_backup_details() {
    local backup_id="$1"
    
    if [[ -z "$backup_id" ]]; then
        log_error "Backup ID is required"
        return 1
    fi
    
    local backup_dir="$BACKUP_BASE_DIR/$backup_id"
    
    if [[ ! -d "$backup_dir" ]]; then
        log_error "Backup not found: $backup_id"
        return 1
    fi
    
    log_step "Showing details for backup: $backup_id"
    
    local metadata_file="$backup_dir/backup-metadata.json"
    
    if [[ -f "$metadata_file" ]]; then
        echo
        echo "Backup Details:"
        echo "==============="
        echo "Backup ID: $backup_id"
        echo "Date: $(jq -r '.date // "Unknown"' "$metadata_file")"
        echo "Domain: $(jq -r '.domain // "Unknown"' "$metadata_file")"
        echo "Environment: $(jq -r '.environment // "Unknown"' "$metadata_file")"
        echo "Script Version: $(jq -r '.script_version // "Unknown"' "$metadata_file")"
        echo "Hostname: $(jq -r '.hostname // "Unknown"' "$metadata_file")"
        echo
        
        # Show verification status
        local verified=$(jq -r '.verification.verified // false' "$metadata_file")
        local verification_time=$(jq -r '.verification.verification_time // "Not verified"' "$metadata_file")
        local total_files=$(jq -r '.verification.total_files // 0' "$metadata_file")
        local failed_verifications=$(jq -r '.verification.failed_verifications // 0' "$metadata_file")
        
        echo "Verification Status:"
        echo "  Verified: $verified"
        echo "  Verification Time: $verification_time"
        echo "  Total Files: $total_files"
        echo "  Failed Verifications: $failed_verifications"
        echo
        
        # Show backed up files by category
        echo "Backed up files by category:"
        jq -r '.backup_files | group_by(.category) | .[] | "\(.[] | .category | select(. != null)) (\(length) files):"' "$metadata_file" | sort | uniq
        echo
        
        # Show individual files
        echo "Individual files:"
        jq -r '.backup_files[] | "  \(.category)/\(.source | split("/") | last) -> \(.backup_path | split("/") | last)"' "$metadata_file" | sort
        echo
        
    else
        echo "Backup metadata not available for: $backup_id"
        echo "Backup directory: $backup_dir"
        echo "Directory size: $(du -sh "$backup_dir" 2>/dev/null | cut -f1 || echo "Unknown")"
        echo
    fi
    
    log_success "Backup details displayed for: $backup_id"
}

# Function to create targeted backup before specific configuration changes
backup_before_modification() {
    local config_type="$1"
    local description="$2"
    
    log_step "Creating backup before $description"
    
    # Ensure backup system is initialized
    if [[ ! -d "$BACKUP_DIR" ]]; then
        init_backup_system
    fi
    
    case "$config_type" in
        "nginx")
            backup_nginx_configs
            ;;
        "postgresql")
            backup_postgresql_configs
            ;;
        "ssl")
            backup_ssl_configs
            ;;
        "firewall")
            backup_firewall_configs
            ;;
        "environment")
            backup_environment_configs
            ;;
        "system")
            backup_system_configs
            ;;
        *)
            log_warning "Unknown configuration type for backup: $config_type"
            return 1
            ;;
    esac
    
    log_success "Backup completed for $description"
}

# Main function to create comprehensive configuration backup
create_configuration_backup() {
    log_section "Creating Configuration Backup"
    
    # Initialize backup system
    init_backup_system
    
    # Backup all configuration categories
    backup_nginx_configs
    backup_postgresql_configs
    backup_ssl_configs
    backup_firewall_configs
    backup_environment_configs
    backup_system_configs
    
    # Verify backup integrity
    if verify_backup_integrity; then
        log_success "Configuration backup completed successfully"
        log_info "Backup location: $BACKUP_DIR"
        log_info "Backup ID: $BACKUP_TIMESTAMP"
        
        # Create a symlink to the latest backup
        ln -sfn "$BACKUP_DIR" "$BACKUP_BASE_DIR/latest"
        log_info "Latest backup symlink created: $BACKUP_BASE_DIR/latest"
        
        # Cleanup old backups
        cleanup_old_backups
        
        return 0
    else
        log_error "Configuration backup completed with errors"
        log_error "Some files may not have been backed up correctly"
        return 1
    fi
}

# Function to save deployment state for potential rollback
save_deployment_state() {
    log_step "Saving deployment state"
    
    # Create a state directory if it doesn't exist
    mkdir -p /var/lib/hauling-deployment/state
    
    # Save current configuration
    if [[ -f ".env" ]]; then
        cp .env /var/lib/hauling-deployment/state/env.backup
    fi
    
    # Save Nginx configuration if it exists
    if [[ -f "/etc/nginx/sites-available/hauling-qr-system" ]]; then
        cp /etc/nginx/sites-available/hauling-qr-system /var/lib/hauling-deployment/state/nginx.backup
    fi
    
    # Save deployment log
    cp "$LOG_FILE" /var/lib/hauling-deployment/state/deployment.log
    
    # Save deployment state information
    cat > /var/lib/hauling-deployment/state/deployment-state.json << EOF
{
  "timestamp": "$(date +%s)",
  "date": "$(date)",
  "domain": "$DOMAIN_NAME",
  "environment": "$ENV_MODE",
  "current_step": "$current_step",
  "completed_steps": [
    "initialization"
    $(if [[ -d "/var/www/hauling-qr-system" ]]; then echo ', "directory_setup"'; fi)
    $(if id "hauling_app" &>/dev/null; then echo ', "user_creation"'; fi)
    $(if [[ -f "/etc/nginx/sites-available/hauling-qr-system" ]]; then echo ', "nginx_config"'; fi)
    $(if systemctl is-active --quiet postgresql; then echo ', "database_setup"'; fi)
    $(if [[ -f "/var/www/hauling-qr-system/client/build/index.html" ]]; then echo ', "frontend_build"'; fi)
    $(if [[ -f "/etc/nginx/ssl/certificate.crt" ]]; then echo ', "ssl_setup"'; fi)
  ]
}
EOF
    
    log_success "Deployment state saved for potential recovery"
}

# ==============================
# Rollback Functions
# ==============================

# Function to restore configurations from backup metadata
restore_from_backup() {
    local backup_id="$1"
    local backup_dir="$BACKUP_BASE_DIR/$backup_id"
    
    log_step "Restoring configurations from backup: $backup_id"
    current_step="restore_from_backup"
    
    # Validate backup directory exists
    if [[ ! -d "$backup_dir" ]]; then
        log_error "Backup directory not found: $backup_dir"
        return 1
    fi
    
    # Validate backup metadata exists
    if [[ ! -f "$backup_dir/backup-metadata.json" ]]; then
        log_error "Backup metadata not found: $backup_dir/backup-metadata.json"
        return 1
    fi
    
    log_info "Reading backup metadata..."
    
    # Parse backup metadata to get list of files to restore
    local backup_files=$(jq -r '.backup_files[] | "\(.backup_path)|\(.source)|\(.permissions)|\(.owner)"' "$backup_dir/backup-metadata.json" 2>/dev/null)
    
    if [[ -z "$backup_files" ]]; then
        log_error "No backup files found in metadata"
        return 1
    fi
    
    local restore_count=0
    local error_count=0
    
    # Process each backup file
    while IFS='|' read -r backup_path source_path permissions owner; do
        if [[ -z "$backup_path" || -z "$source_path" ]]; then
            continue
        fi
        
        log_info "Restoring: $source_path"
        
        # Create directory if it doesn't exist
        local source_dir=$(dirname "$source_path")
        if [[ ! -d "$source_dir" ]]; then
            log_debug "Creating directory: $source_dir"
            mkdir -p "$source_dir"
        fi
        
        # Restore the file
        if cp "$backup_path" "$source_path" 2>/dev/null; then
            log_debug "File restored: $source_path"
            
            # Restore permissions if available
            if [[ -n "$permissions" && "$permissions" != "unknown" ]]; then
                chmod "$permissions" "$source_path" 2>/dev/null || log_warning "Failed to restore permissions for $source_path"
            fi
            
            # Restore ownership if available and we're running as root
            if [[ -n "$owner" && "$owner" != "unknown" && "$EUID" -eq 0 ]]; then
                chown "$owner" "$source_path" 2>/dev/null || log_warning "Failed to restore ownership for $source_path"
            fi
            
            ((restore_count++))
        else
            log_error "Failed to restore file: $source_path"
            ((error_count++))
        fi
        
    done <<< "$backup_files"
    
    log_info "Restore summary: $restore_count files restored, $error_count errors"
    
    if [[ $error_count -gt 0 ]]; then
        log_warning "Some files could not be restored. Check the logs for details."
        return 1
    fi
    
    log_success "All configurations restored successfully from backup: $backup_id"
    return 0
}

# Function to stop services before rollback
stop_services_for_rollback() {
    log_step "Stopping services for rollback"
    
    local services_stopped=()
    
    # Stop PM2 processes
    if command -v pm2 &> /dev/null; then
        log_info "Stopping PM2 processes..."
        if pm2 stop all 2>/dev/null; then
            services_stopped+=("pm2")
            log_debug "PM2 processes stopped"
        else
            log_warning "Failed to stop PM2 processes or no processes running"
        fi
    fi
    
    # Stop Nginx
    if service_is_active nginx; then
        log_info "Stopping Nginx..."
        if service_stop nginx; then
            services_stopped+=("nginx")
            log_debug "Nginx stopped"
        else
            log_error "Failed to stop Nginx"
            return 1
        fi
    fi

    # Stop PostgreSQL if it was installed by this deployment
    if service_is_active postgresql && [[ -f "/var/lib/hauling-deployment/postgresql-installed" ]]; then
        log_info "Stopping PostgreSQL..."
        if service_stop postgresql; then
            services_stopped+=("postgresql")
            log_debug "PostgreSQL stopped"
        else
            log_warning "Failed to stop PostgreSQL"
        fi
    fi
    
    # Save list of stopped services for restart
    printf '%s\n' "${services_stopped[@]}" > /tmp/rollback-stopped-services.txt
    
    log_success "Services stopped for rollback: ${services_stopped[*]}"
    return 0
}

# Function to start services after rollback
start_services_after_rollback() {
    log_step "Starting services after rollback"
    
    local services_file="/tmp/rollback-stopped-services.txt"
    
    if [[ ! -f "$services_file" ]]; then
        log_warning "No services list found for restart"
        return 0
    fi
    
    local services_started=()
    local error_count=0
    
    # Read services that were stopped
    while IFS= read -r service; do
        if [[ -z "$service" ]]; then
            continue
        fi
        
        log_info "Starting service: $service"
        
        case "$service" in
            "nginx")
                if service_start nginx; then
                    services_started+=("nginx")
                    log_debug "Nginx started successfully"
                else
                    log_error "Failed to start Nginx"
                    ((error_count++))
                fi
                ;;
            "postgresql")
                if service_start postgresql; then
                    services_started+=("postgresql")
                    log_debug "PostgreSQL started successfully"
                else
                    log_error "Failed to start PostgreSQL"
                    ((error_count++))
                fi
                ;;
            "pm2")
                if command -v pm2 &> /dev/null; then
                    # Try to restart PM2 processes from saved ecosystem
                    if [[ -f "/var/www/hauling-qr-system/ecosystem.config.js" ]]; then
                        cd /var/www/hauling-qr-system
                        if pm2 start ecosystem.config.js; then
                            services_started+=("pm2")
                            log_debug "PM2 processes started successfully"
                        else
                            log_error "Failed to start PM2 processes"
                            ((error_count++))
                        fi
                    else
                        log_warning "PM2 ecosystem file not found, skipping PM2 restart"
                    fi
                fi
                ;;
            *)
                log_warning "Unknown service for restart: $service"
                ;;
        esac
        
    done < "$services_file"
    
    # Clean up temporary file
    rm -f "$services_file"
    
    log_info "Service restart summary: ${#services_started[@]} started, $error_count errors"
    
    if [[ $error_count -gt 0 ]]; then
        log_warning "Some services could not be restarted. Check the logs for details."
        return 1
    fi
    
    log_success "All services restarted successfully: ${services_started[*]}"
    return 0
}

# Function to validate system state after rollback
validate_rollback_state() {
    log_step "Validating system state after rollback"
    
    local validation_errors=0
    
    # Check if Nginx configuration is valid
    if command -v nginx &> /dev/null; then
        log_info "Validating Nginx configuration..."
        if nginx -t 2>/dev/null; then
            log_debug "Nginx configuration is valid"
        else
            log_error "Nginx configuration validation failed"
            ((validation_errors++))
        fi
    fi
    
    # Check if PostgreSQL is accessible
    if systemctl is-active --quiet postgresql; then
        log_info "Validating PostgreSQL connection..."
        if sudo -u postgres psql -c "SELECT 1;" &>/dev/null; then
            log_debug "PostgreSQL connection successful"
        else
            log_error "PostgreSQL connection failed"
            ((validation_errors++))
        fi
    fi
    
    # Check if application directory exists and has proper structure
    if [[ -d "/var/www/hauling-qr-system" ]]; then
        log_info "Validating application directory structure..."
        local required_dirs=("client" "server" "database")
        for dir in "${required_dirs[@]}"; do
            if [[ ! -d "/var/www/hauling-qr-system/$dir" ]]; then
                log_error "Missing application directory: $dir"
                ((validation_errors++))
            fi
        done
    fi
    
    # Check if services are running as expected
    log_info "Validating service status..."
    
    if systemctl is-enabled --quiet nginx 2>/dev/null; then
        if ! systemctl is-active --quiet nginx; then
            log_error "Nginx is enabled but not running"
            ((validation_errors++))
        fi
    fi
    
    if systemctl is-enabled --quiet postgresql 2>/dev/null; then
        if ! systemctl is-active --quiet postgresql; then
            log_error "PostgreSQL is enabled but not running"
            ((validation_errors++))
        fi
    fi
    
    # Check if PM2 processes are running if PM2 is installed
    if command -v pm2 &> /dev/null; then
        local pm2_processes=$(pm2 list | grep -c "online" 2>/dev/null || echo "0")
        if [[ "$pm2_processes" -eq 0 ]]; then
            log_warning "No PM2 processes are running"
        else
            log_debug "PM2 processes running: $pm2_processes"
        fi
    fi
    
    if [[ $validation_errors -eq 0 ]]; then
        log_success "System state validation passed"
        return 0
    else
        log_error "System state validation failed with $validation_errors errors"
        return 1
    fi
}

# Main rollback function
rollback_deployment() {
    local backup_id="$1"
    local force_rollback="$2"
    
    log_section "Starting Deployment Rollback"
    current_step="rollback_deployment"
    
    # If no backup ID provided, find the latest backup
    if [[ -z "$backup_id" ]]; then
        log_info "No backup ID provided, searching for latest backup..."
        
        if [[ ! -d "$BACKUP_BASE_DIR" ]]; then
            log_error "No backup directory found: $BACKUP_BASE_DIR"
            log_error "Cannot perform rollback without existing backups"
            return 1
        fi
        
        # Find the latest backup directory
        backup_id=$(ls -1t "$BACKUP_BASE_DIR" | head -n1)
        
        if [[ -z "$backup_id" ]]; then
            log_error "No backups found in $BACKUP_BASE_DIR"
            return 1
        fi
        
        log_info "Using latest backup: $backup_id"
    fi
    
    local backup_dir="$BACKUP_BASE_DIR/$backup_id"
    
    # Validate backup exists
    if [[ ! -d "$backup_dir" ]]; then
        log_error "Backup not found: $backup_dir"
        return 1
    fi
    
    # Show backup information
    if [[ -f "$backup_dir/backup-metadata.json" ]]; then
        local backup_date=$(jq -r '.date' "$backup_dir/backup-metadata.json" 2>/dev/null || echo "Unknown")
        local backup_domain=$(jq -r '.domain' "$backup_dir/backup-metadata.json" 2>/dev/null || echo "Unknown")
        local backup_env=$(jq -r '.environment' "$backup_dir/backup-metadata.json" 2>/dev/null || echo "Unknown")
        
        log_info "Backup Information:"
        log_info "  Date: $backup_date"
        log_info "  Domain: $backup_domain"
        log_info "  Environment: $backup_env"
        log_info "  Backup ID: $backup_id"
    fi
    
    # Confirm rollback operation unless forced
    if [[ "$force_rollback" != "true" && "$INTERACTIVE" == true ]]; then
        echo
        echo "WARNING: This will restore system configurations from the backup."
        echo "Current configurations will be overwritten."
        echo
        read -p "Are you sure you want to proceed with rollback? (yes/no): " confirm_rollback
        
        if [[ "$confirm_rollback" != "yes" ]]; then
            log_info "Rollback cancelled by user"
            return 0
        fi
    fi
    
    # Create a backup of current state before rollback
    log_info "Creating backup of current state before rollback..."
    local pre_rollback_backup_id="pre-rollback-$(date +%Y%m%d_%H%M%S)"
    local original_backup_timestamp="$BACKUP_TIMESTAMP"
    local original_backup_dir="$BACKUP_DIR"
    
    BACKUP_TIMESTAMP="$pre_rollback_backup_id"
    BACKUP_DIR="$BACKUP_BASE_DIR/$pre_rollback_backup_id"
    
    if init_backup_system && create_configuration_backup; then
        log_success "Pre-rollback backup created: $pre_rollback_backup_id"
    else
        log_warning "Failed to create pre-rollback backup, continuing anyway..."
    fi
    
    # Restore original backup variables
    BACKUP_TIMESTAMP="$original_backup_timestamp"
    BACKUP_DIR="$original_backup_dir"
    
    # Start rollback process
    local rollback_start_time=$(date +%s)
    
    # Step 1: Stop services
    if ! stop_services_for_rollback; then
        log_error "Failed to stop services for rollback"
        return 1
    fi
    
    # Step 2: Restore configurations
    if ! restore_from_backup "$backup_id"; then
        log_error "Failed to restore configurations from backup"
        
        # Attempt to restart services even if restore failed
        start_services_after_rollback
        
        # Generate failure report
        generate_rollback_report "$backup_id" "$(($(date +%s) - rollback_start_time))" "failed_restore" "$pre_rollback_backup_id"
        return 1
    fi
    
    # Step 3: Start services
    if ! start_services_after_rollback; then
        log_error "Failed to restart services after rollback"
        
        # Generate failure report
        generate_rollback_report "$backup_id" "$(($(date +%s) - rollback_start_time))" "failed_service_restart" "$pre_rollback_backup_id"
        return 1
    fi
    
    # Step 4: Validate system state
    if ! validate_rollback_state; then
        log_error "System state validation failed after rollback"
        
        # Generate failure report
        generate_rollback_report "$backup_id" "$(($(date +%s) - rollback_start_time))" "failed_validation" "$pre_rollback_backup_id"
        return 1
    fi
    
    # Calculate rollback duration
    local rollback_end_time=$(date +%s)
    local rollback_duration=$((rollback_end_time - rollback_start_time))
    
    # Generate rollback report
    generate_rollback_report "$backup_id" "$rollback_duration" "success" "$pre_rollback_backup_id"
    
    log_success "Deployment rollback completed successfully"
    log_info "Rollback duration: ${rollback_duration} seconds"
    log_info "System restored to backup: $backup_id"
    
    return 0
}

# Function to generate rollback report
generate_rollback_report() {
    local backup_id="$1"
    local duration="$2"
    local status="$3"
    local pre_rollback_backup="${4:-not_created}"
    
    local report_file="/var/log/hauling-deployment/rollback-report-$(date +%Y%m%d_%H%M%S).json"
    
    cat > "$report_file" << EOF
{
  "rollback_report": {
    "timestamp": "$(date +%s)",
    "date": "$(date)",
    "backup_id": "$backup_id",
    "status": "$status",
    "duration_seconds": $duration,
    "hostname": "$(hostname)",
    "domain": "$DOMAIN_NAME",
    "environment": "$ENV_MODE",
    "script_version": "$VERSION",
    "rollback_details": {
      "services_managed": $(cat /tmp/rollback-stopped-services.txt 2>/dev/null | jq -R . | jq -s . || echo '[]'),
      "validation_passed": $(if [[ "$status" == "success" ]]; then echo "true"; else echo "false"; fi),
      "pre_rollback_backup": "$pre_rollback_backup"
    }
  }
}
EOF
    
    log_info "Rollback report generated: $report_file"
    
    # Also log to main deployment log
    log_info "=== ROLLBACK REPORT ==="
    log_info "Status: $status"
    log_info "Backup ID: $backup_id"
    log_info "Duration: ${duration}s"
    log_info "Report file: $report_file"
    log_info "======================="
}

# Function to list available backups for rollback
list_available_backups() {
    log_step "Listing available backups"
    
    if [[ ! -d "$BACKUP_BASE_DIR" ]]; then
        log_info "No backup directory found: $BACKUP_BASE_DIR"
        return 0
    fi
    
    local backup_count=0
    
    echo
    echo "Available Backups:"
    echo "=================="
    
    for backup_dir in "$BACKUP_BASE_DIR"/*; do
        if [[ -d "$backup_dir" ]]; then
            local backup_id=$(basename "$backup_dir")
            local metadata_file="$backup_dir/backup-metadata.json"
            
            if [[ -f "$metadata_file" ]]; then
                local backup_date=$(jq -r '.date' "$metadata_file" 2>/dev/null || echo "Unknown")
                local backup_domain=$(jq -r '.domain' "$metadata_file" 2>/dev/null || echo "Unknown")
                local backup_env=$(jq -r '.environment' "$metadata_file" 2>/dev/null || echo "Unknown")
                local file_count=$(jq -r '.backup_files | length' "$metadata_file" 2>/dev/null || echo "0")
                
                echo "Backup ID: $backup_id"
                echo "  Date: $backup_date"
                echo "  Domain: $backup_domain"
                echo "  Environment: $backup_env"
                echo "  Files: $file_count"
                echo
                
                ((backup_count++))
            else
                echo "Backup ID: $backup_id (metadata missing)"
                echo
            fi
        fi
    done
    
    if [[ $backup_count -eq 0 ]]; then
        echo "No backups found."
    else
        echo "Total backups: $backup_count"
        echo
        echo "To rollback to a specific backup, use:"
        echo "  ./deploy-hauling-qr-ubuntu.sh --rollback [backup_id]"
        echo
        echo "To rollback to the latest backup, use:"
        echo "  ./deploy-hauling-qr-ubuntu.sh --rollback"
    fi
}

# ==============================
# Configuration Functions
# ==============================

source_config_file() {
    local config_file=$1
    log_step "Loading configuration from $config_file"
    current_step="config_loading"
    
    if [[ ! -f "$config_file" ]]; then
        log_error "Configuration file not found: $config_file"
        log_error "Please provide a valid path to your configuration file"
        log_info "You can create a configuration file using one of the templates:"
        log_info "- deployment-config-template.conf (Shell format)"
        log_info "- deployment-config-template.json (JSON format)"
        log_info "- deployment-config-template.yaml (YAML format)"
        exit 1
    fi
    
    # Detect file format based on extension
    local file_extension="${config_file##*.}"
    local file_format="unknown"
    
    case "$file_extension" in
        conf|env|sh)
            file_format="shell"
            ;;
        json)
            file_format="json"
            ;;
        yaml|yml)
            file_format="yaml"
            ;;
        *)
            # Try to detect format based on content
            log_info "File extension not recognized, attempting to detect format from content"
            if grep -q "^{" "$config_file"; then
                file_format="json"
                log_info "Content appears to be JSON format"
            elif grep -q "^[a-zA-Z0-9_]*:" "$config_file"; then
                file_format="yaml"
                log_info "Content appears to be YAML format"
            else
                file_format="shell"
                log_info "Assuming shell format by default"
            fi
            ;;
    esac
    
    log_info "Detected configuration file format: $file_format"
    
    # Parse configuration based on format
    case "$file_format" in
        shell)
            # Source the shell-format configuration file
            log_info "Parsing shell format configuration file"
            parse_shell_config "$config_file"
            ;;
        json)
            parse_json_config "$config_file"
            ;;
        yaml)
            parse_yaml_config "$config_file"
            ;;
        *)
            log_error "Unsupported configuration file format"
            log_error "Supported formats: shell (.conf, .sh, .env), JSON (.json), YAML (.yaml, .yml)"
            exit 1
            ;;
    esac
    
    # Validate the loaded configuration
    validate_loaded_configuration
    
    log_success "Configuration loaded from $config_file"
}

# Function to parse JSON configuration file
parse_json_config() {
    local config_file=$1
    log_info "Parsing JSON configuration file"
    
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        log_warning "jq is not installed. Installing..."
        apt-get update -y
        apt-get install -y jq
        
        # Verify installation
        if ! command -v jq &> /dev/null; then
            log_error "Failed to install jq. Please install it manually and try again."
            log_error "You can install jq with: apt-get install -y jq"
            exit 1
        fi
    fi
    
    # Validate JSON syntax
    local json_validation_error=$(jq empty "$config_file" 2>&1 || echo "error")
    if [[ "$json_validation_error" == "error" ]]; then
        log_error "Invalid JSON syntax in configuration file"
        log_error "$(jq empty "$config_file" 2>&1)"
        log_error "Please fix the JSON syntax errors and try again"
        exit 1
    fi
    
    # Extract configuration values
    local json_content=$(cat "$config_file")
    
    log_info "Extracting configuration values from JSON"
    
    # Domain configuration
    if jq -e '.domain' "$config_file" > /dev/null 2>&1; then
        DOMAIN_NAME=$(jq -r '.domain.name // empty' <<< "$json_content")
        SSL_MODE=$(jq -r '.domain.sslMode // empty' <<< "$json_content")
        
        # Cloudflare specific settings
        if jq -e '.domain.cloudflare' "$config_file" > /dev/null 2>&1; then
            CLOUDFLARE_SSL_MODE=$(jq -r '.domain.cloudflare.mode // empty' <<< "$json_content")
            CLOUDFLARE_API_TOKEN=$(jq -r '.domain.cloudflare.apiToken // empty' <<< "$json_content")
            CLOUDFLARE_ZONE_ID=$(jq -r '.domain.cloudflare.zoneId // empty' <<< "$json_content")
            
            # Cloudflare optimization settings
            if jq -e '.domain.cloudflare.optimization' "$config_file" > /dev/null 2>&1; then
                CF_MINIFY_HTML=$(jq -r '.domain.cloudflare.optimization.minifyHtml // empty' <<< "$json_content")
                CF_MINIFY_CSS=$(jq -r '.domain.cloudflare.optimization.minifyCss // empty' <<< "$json_content")
                CF_MINIFY_JS=$(jq -r '.domain.cloudflare.optimization.minifyJs // empty' <<< "$json_content")
                CF_BROTLI=$(jq -r '.domain.cloudflare.optimization.brotli // empty' <<< "$json_content")
                CF_CACHE_LEVEL=$(jq -r '.domain.cloudflare.optimization.cacheLevel // empty' <<< "$json_content")
                CF_BROWSER_CACHE_TTL=$(jq -r '.domain.cloudflare.optimization.browserCacheTtl // empty' <<< "$json_content")
            fi
        fi
    else
        # Legacy format support
        DOMAIN_NAME=$(jq -r '.domainName // empty' <<< "$json_content")
        SSL_MODE=$(jq -r '.sslMode // empty' <<< "$json_content")
    fi
    
    # Database configuration
    if jq -e '.database' "$config_file" > /dev/null 2>&1; then
        DB_PASSWORD=$(jq -r '.database.password // empty' <<< "$json_content")
        DB_HOST=$(jq -r '.database.host // empty' <<< "$json_content")
        DB_PORT=$(jq -r '.database.port // empty' <<< "$json_content")
        DB_NAME=$(jq -r '.database.name // empty' <<< "$json_content")
        DB_USER=$(jq -r '.database.user // empty' <<< "$json_content")
        DB_POOL_MAX=$(jq -r '.database.poolMax // empty' <<< "$json_content")
        DB_POOL_MIN=$(jq -r '.database.poolMin // empty' <<< "$json_content")
        
        # Database optimization settings
        if jq -e '.database.optimization' "$config_file" > /dev/null 2>&1; then
            DB_SHARED_BUFFERS=$(jq -r '.database.optimization.sharedBuffers // empty' <<< "$json_content")
            DB_EFFECTIVE_CACHE_SIZE=$(jq -r '.database.optimization.effectiveCacheSize // empty' <<< "$json_content")
            DB_WORK_MEM=$(jq -r '.database.optimization.workMem // empty' <<< "$json_content")
            DB_MAX_CONNECTIONS=$(jq -r '.database.optimization.maxConnections // empty' <<< "$json_content")
        fi
    else
        # Legacy format support
        DB_PASSWORD=$(jq -r '.dbPassword // empty' <<< "$json_content")
    fi
    
    # Security configuration
    if jq -e '.security' "$config_file" > /dev/null 2>&1; then
        JWT_SECRET=$(jq -r '.security.jwtSecret // empty' <<< "$json_content")
        JWT_EXPIRATION=$(jq -r '.security.jwtExpiration // empty' <<< "$json_content")
        JWT_REFRESH_SECRET=$(jq -r '.security.jwtRefreshSecret // empty' <<< "$json_content")
        JWT_REFRESH_EXPIRATION=$(jq -r '.security.jwtRefreshExpiration // empty' <<< "$json_content")
        
        # Fail2Ban settings
        if jq -e '.security.fail2ban' "$config_file" > /dev/null 2>&1; then
            FAIL2BAN_BANTIME=$(jq -r '.security.fail2ban.banTime // empty' <<< "$json_content")
            FAIL2BAN_MAXRETRY=$(jq -r '.security.fail2ban.maxRetry // empty' <<< "$json_content")
        fi
        
        # Firewall settings
        if jq -e '.security.firewall' "$config_file" > /dev/null 2>&1; then
            UFW_ENABLE=$(jq -r '.security.firewall.enable // empty' <<< "$json_content")
        fi
        
        # Rate limiting settings
        if jq -e '.security.rateLimiting' "$config_file" > /dev/null 2>&1; then
            NGINX_RATE_LIMIT_API=$(jq -r '.security.rateLimiting.api // empty' <<< "$json_content")
            NGINX_RATE_LIMIT_AUTH=$(jq -r '.security.rateLimiting.auth // empty' <<< "$json_content")
            NGINX_RATE_LIMIT_GENERAL=$(jq -r '.security.rateLimiting.general // empty' <<< "$json_content")
        fi
    else
        # Legacy format support
        JWT_SECRET=$(jq -r '.jwtSecret // empty' <<< "$json_content")
    fi
    
    # Admin user configuration
    if jq -e '.admin' "$config_file" > /dev/null 2>&1; then
        ADMIN_USERNAME=$(jq -r '.admin.username // empty' <<< "$json_content")
        ADMIN_PASSWORD=$(jq -r '.admin.password // empty' <<< "$json_content")
        ADMIN_EMAIL=$(jq -r '.admin.email // empty' <<< "$json_content")
    else
        # Legacy format support
        ADMIN_USERNAME=$(jq -r '.adminUsername // empty' <<< "$json_content")
        ADMIN_PASSWORD=$(jq -r '.adminPassword // empty' <<< "$json_content")
        ADMIN_EMAIL=$(jq -r '.adminEmail // empty' <<< "$json_content")
    fi
    
    # Repository configuration
    if jq -e '.repository' "$config_file" > /dev/null 2>&1; then
        REPO_URL=$(jq -r '.repository.url // empty' <<< "$json_content")
        REPO_BRANCH=$(jq -r '.repository.branch // empty' <<< "$json_content")
    else
        # Legacy format support
        REPO_URL=$(jq -r '.repoUrl // empty' <<< "$json_content")
        REPO_BRANCH=$(jq -r '.repoBranch // empty' <<< "$json_content")
    fi
    
    # Advanced configuration
    if [[ $(jq 'has("advanced")' <<< "$json_content") == "true" ]]; then
        local advanced=$(jq '.advanced' <<< "$json_content")
        
        # Server configuration
        NODE_VERSION=$(jq -r '.nodeVersion // empty' <<< "$advanced")
        POSTGRES_VERSION=$(jq -r '.postgresVersion // empty' <<< "$advanced")
        APP_USER=$(jq -r '.appUser // empty' <<< "$advanced")
        APP_DIR=$(jq -r '.appDir // empty' <<< "$advanced")
        
        # Performance settings
        PM2_INSTANCES=$(jq -r '.pm2Instances // empty' <<< "$advanced")
        MAX_MEMORY_RESTART=$(jq -r '.maxMemoryRestart // empty' <<< "$advanced")
        NODE_MAX_OLD_SPACE=$(jq -r '.nodeMaxOldSpace // empty' <<< "$advanced")
        
        # Security settings
        FAIL2BAN_BANTIME=$(jq -r '.fail2banBantime // empty' <<< "$advanced")
        FAIL2BAN_MAXRETRY=$(jq -r '.fail2banMaxretry // empty' <<< "$advanced")
        UFW_ENABLE=$(jq -r '.ufwEnable // empty' <<< "$advanced")
        
        # SSL configuration
        SSL_CERT_PATH=$(jq -r '.sslCertPath // empty' <<< "$advanced")
        SSL_KEY_PATH=$(jq -r '.sslKeyPath // empty' <<< "$advanced")
        SSL_COUNTRY=$(jq -r '.sslCountry // empty' <<< "$advanced")
        SSL_STATE=$(jq -r '.sslState // empty' <<< "$advanced")
        SSL_CITY=$(jq -r '.sslCity // empty' <<< "$advanced")
        SSL_ORG=$(jq -r '.sslOrg // empty' <<< "$advanced")
        
        # Rate limiting
        NGINX_RATE_LIMIT_API=$(jq -r '.nginxRateLimitApi // empty' <<< "$advanced")
        NGINX_RATE_LIMIT_AUTH=$(jq -r '.nginxRateLimitAuth // empty' <<< "$advanced")
        NGINX_RATE_LIMIT_GENERAL=$(jq -r '.nginxRateLimitGeneral // empty' <<< "$advanced")
        
        # Database optimization
        DB_SHARED_BUFFERS=$(jq -r '.dbSharedBuffers // empty' <<< "$advanced")
        DB_EFFECTIVE_CACHE_SIZE=$(jq -r '.dbEffectiveCacheSize // empty' <<< "$advanced")
        DB_WORK_MEM=$(jq -r '.dbWorkMem // empty' <<< "$advanced")
        DB_MAX_CONNECTIONS=$(jq -r '.dbMaxConnections // empty' <<< "$advanced")
        
        # Monitoring configuration
        HEALTH_CHECK_INTERVAL=$(jq -r '.healthCheckInterval // empty' <<< "$advanced")
        PERFORMANCE_CHECK_INTERVAL=$(jq -r '.performanceCheckInterval // empty' <<< "$advanced")
        REPORT_GENERATION_TIME=$(jq -r '.reportGenerationTime // empty' <<< "$advanced")
        
        # Backup configuration
        FULL_BACKUP_SCHEDULE=$(jq -r '.fullBackupSchedule // empty' <<< "$advanced")
        BACKUP_COMPRESSION=$(jq -r '.backupCompression // empty' <<< "$advanced")
        
        # Logging configuration
        LOG_LEVEL=$(jq -r '.logLevel // empty' <<< "$advanced")
        LOG_ROTATION_SIZE=$(jq -r '.logRotationSize // empty' <<< "$advanced")
        LOG_RETENTION_DAYS=$(jq -r '.logRetentionDays // empty' <<< "$advanced")
        
        # Email configuration
        SMTP_HOST=$(jq -r '.smtpHost // empty' <<< "$advanced")
        SMTP_PORT=$(jq -r '.smtpPort // empty' <<< "$advanced")
        SMTP_USER=$(jq -r '.smtpUser // empty' <<< "$advanced")
        SMTP_PASSWORD=$(jq -r '.smtpPassword // empty' <<< "$advanced")
        ALERT_EMAIL=$(jq -r '.alertEmail // empty' <<< "$advanced")
    fi
    
    # Cloudflare configuration
    if [[ $(jq 'has("cloudflare")' <<< "$json_content") == "true" ]]; then
        local cloudflare=$(jq '.cloudflare' <<< "$json_content")
        
        CLOUDFLARE_SSL_MODE=$(jq -r '.sslMode // empty' <<< "$cloudflare")
        CLOUDFLARE_API_TOKEN=$(jq -r '.apiToken // empty' <<< "$cloudflare")
        CLOUDFLARE_ZONE_ID=$(jq -r '.zoneId // empty' <<< "$cloudflare")
        CF_MINIFY_HTML=$(jq -r '.minifyHtml // empty' <<< "$cloudflare")
        CF_MINIFY_CSS=$(jq -r '.minifyCss // empty' <<< "$cloudflare")
        CF_MINIFY_JS=$(jq -r '.minifyJs // empty' <<< "$cloudflare")
        CF_BROTLI=$(jq -r '.brotli // empty' <<< "$cloudflare")
        CF_CACHE_LEVEL=$(jq -r '.cacheLevel // empty' <<< "$cloudflare")
        CF_BROWSER_CACHE_TTL=$(jq -r '.browserCacheTtl // empty' <<< "$cloudflare")
    fi
    
    log_success "JSON configuration parsed successfully"
}

# Function to parse YAML configuration file with enhanced validation
parse_yaml_config() {
    local config_file=$1
    log_info "Parsing YAML configuration file: $config_file"
    
    # Check if file exists and is readable
    if [[ ! -f "$config_file" ]]; then
        log_error "Configuration file not found: $config_file"
        exit 1
    fi
    
    if [[ ! -r "$config_file" ]]; then
        log_error "Configuration file is not readable: $config_file"
        exit 1
    fi
    
    # Check file size (prevent extremely large files)
    local file_size=$(stat -c%s "$config_file")
    if [[ $file_size -gt 1048576 ]]; then  # 1MB limit
        log_error "Configuration file is too large (${file_size} bytes). Maximum allowed: 1MB"
        exit 1
    fi
    
    # Install yq if not available
    if ! command -v yq &> /dev/null; then
        log_info "Installing yq YAML processor..."
        install_yq_processor
    fi
    
    # Validate YAML syntax with detailed error reporting
    local yaml_validation_output
    yaml_validation_output=$(yq eval . "$config_file" 2>&1)
    local validation_exit_code=$?
    
    if [[ $validation_exit_code -ne 0 ]]; then
        log_error "Invalid YAML syntax in configuration file"
        log_error "Validation error: $yaml_validation_output"
        
        # Try to provide more helpful error information
        local line_number=$(echo "$yaml_validation_output" | grep -o "line [0-9]*" | head -n1 | grep -o "[0-9]*")
        if [[ -n "$line_number" ]]; then
            log_error "Error appears to be around line $line_number:"
            sed -n "$((line_number-2)),$((line_number+2))p" "$config_file" | nl -ba -v$((line_number-2))
        fi
        exit 1
    fi
    
    # Validate YAML structure and required fields
    validate_yaml_structure "$config_file"
    
    # Extract all configuration values using enhanced functions
    extract_yaml_values "$config_file"
    
    # Log configuration summary (with sensitive data masked)
    log_info "Configuration summary:"
    log_info "  Domain: $DOMAIN_NAME"
    log_info "  SSL Mode: $SSL_MODE"
    log_info "  Environment: $ENV_MODE"
    log_info "  Monitoring: $MONITORING_ENABLED"
    log_info "  Backups: $BACKUP_ENABLED"
    log_info "  Admin User: $ADMIN_USERNAME"
    log_info "  Admin Email: $ADMIN_EMAIL"
    [[ -n "$DB_PASSWORD" ]] && log_info "  Database Password: [PROVIDED]" || log_info "  Database Password: [WILL BE GENERATED]"
    [[ -n "$JWT_SECRET" ]] && log_info "  JWT Secret: [PROVIDED]" || log_info "  JWT Secret: [WILL BE GENERATED]"
    [[ -n "$ADMIN_PASSWORD" ]] && log_info "  Admin Password: [PROVIDED]" || log_info "  Admin Password: [WILL BE GENERATED]"
    
    log_success "YAML configuration parsed and validated successfully"
}

validate_configuration() {
    log_step "Validating configuration"
    current_step="config_validation"
    
    local validation_errors=0
    local validation_warnings=0
    local error_messages=""
    local warning_messages=""
    
    # Function to add validation error
    add_validation_error() {
        local message="$1"
        validation_errors=$((validation_errors + 1))
        error_messages="${error_messages}${validation_errors}. $message\n"
        log_error "$message"
    }
    
    # Function to add validation warning
    add_validation_warning() {
        local message="$1"
        validation_warnings=$((validation_warnings + 1))
        warning_messages="${warning_messages}${validation_warnings}. $message\n"
        log_warning "$message"
    }
    
    # Check required parameters
    if [[ -z "$DOMAIN_NAME" ]]; then
        add_validation_error "Domain name is required (DOMAIN_NAME)"
    else
        # Validate domain name format
        if ! [[ "$DOMAIN_NAME" =~ ^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$ ]]; then
            add_validation_error "Invalid domain name format: $DOMAIN_NAME"
        fi
    fi
    
    # Validate SSL mode
    if [[ -z "$SSL_MODE" ]]; then
        add_validation_warning "SSL mode not specified, defaulting to 'cloudflare'"
        SSL_MODE="cloudflare"
    elif [[ "$SSL_MODE" != "none" && "$SSL_MODE" != "letsencrypt" && "$SSL_MODE" != "cloudflare" && "$SSL_MODE" != "custom" ]]; then
        add_validation_error "Invalid SSL mode: $SSL_MODE. Must be one of: none, letsencrypt, cloudflare, custom"
    fi
    
    # Validate custom SSL certificates if using custom SSL mode
    if [[ "$SSL_MODE" == "custom" ]]; then
        if [[ -z "$SSL_CERT_PATH" || -z "$SSL_KEY_PATH" ]]; then
            add_validation_error "Custom SSL mode requires SSL_CERT_PATH and SSL_KEY_PATH to be set"
        else
            if [[ ! -f "$SSL_CERT_PATH" ]]; then
                add_validation_error "SSL certificate file not found: $SSL_CERT_PATH"
            fi
            
            if [[ ! -f "$SSL_KEY_PATH" ]]; then
                add_validation_error "SSL key file not found: $SSL_KEY_PATH"
            fi
        fi
    fi
    
    # Validate environment mode
    if [[ -z "$ENV_MODE" ]]; then
        add_validation_warning "Environment mode not specified, defaulting to 'production'"
        ENV_MODE="production"
    elif [[ "$ENV_MODE" != "production" && "$ENV_MODE" != "staging" && "$ENV_MODE" != "development" ]]; then
        add_validation_error "Invalid environment mode: $ENV_MODE. Must be one of: production, staging, development"
    fi
    
    # Validate repository URL if provided
    if [[ -n "$REPO_URL" ]]; then
        if ! [[ "$REPO_URL" =~ ^https?://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/ ]]; then
            add_validation_warning "Repository URL format may be invalid: $REPO_URL"
        fi
    fi
    
    # Validate email addresses
    if [[ -n "$ADMIN_EMAIL" ]]; then
        if ! [[ "$ADMIN_EMAIL" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
            add_validation_error "Invalid admin email format: $ADMIN_EMAIL"
        fi
    else
        add_validation_warning "Admin email not specified"
    fi
    
    # Validate boolean values
    for bool_var in MONITORING_ENABLED BACKUP_ENABLED UFW_ENABLE BACKUP_COMPRESSION; do
        if [[ -n "${!bool_var}" && "${!bool_var}" != "true" && "${!bool_var}" != "false" ]]; then
            add_validation_warning "Invalid boolean value for $bool_var: ${!bool_var}. Must be 'true' or 'false'. Defaulting to 'true'"
            eval "$bool_var=true"
        fi
    done
    
    # Validate numeric values
    if [[ -n "$BACKUP_RETENTION_DAYS" && ! "$BACKUP_RETENTION_DAYS" =~ ^[0-9]+$ ]]; then
        add_validation_warning "Invalid numeric value for BACKUP_RETENTION_DAYS: $BACKUP_RETENTION_DAYS. Must be a positive integer. Defaulting to 7"
        BACKUP_RETENTION_DAYS=7
    fi
    
    if [[ -n "$FAIL2BAN_BANTIME" && ! "$FAIL2BAN_BANTIME" =~ ^[0-9]+$ ]]; then
        add_validation_warning "Invalid numeric value for FAIL2BAN_BANTIME: $FAIL2BAN_BANTIME. Must be a positive integer. Defaulting to 3600"
        FAIL2BAN_BANTIME=3600
    fi
    
    if [[ -n "$FAIL2BAN_MAXRETRY" && ! "$FAIL2BAN_MAXRETRY" =~ ^[0-9]+$ ]]; then
        add_validation_warning "Invalid numeric value for FAIL2BAN_MAXRETRY: $FAIL2BAN_MAXRETRY. Must be a positive integer. Defaulting to 5"
        FAIL2BAN_MAXRETRY=5
    fi
    
    # Validate Cloudflare settings if using Cloudflare SSL mode
    if [[ "$SSL_MODE" == "cloudflare" ]]; then
        if [[ -z "$CLOUDFLARE_SSL_MODE" ]]; then
            add_validation_warning "Cloudflare SSL mode not specified, defaulting to 'full'"
            CLOUDFLARE_SSL_MODE="full"
        elif [[ "$CLOUDFLARE_SSL_MODE" != "flexible" && "$CLOUDFLARE_SSL_MODE" != "full" && "$CLOUDFLARE_SSL_MODE" != "full_strict" ]]; then
            add_validation_error "Invalid Cloudflare SSL mode: $CLOUDFLARE_SSL_MODE. Must be one of: flexible, full, full_strict"
        fi
    fi
    
    # Check for conflicting settings
    if [[ "$SSL_MODE" == "none" && "$ENV_MODE" == "production" ]]; then
        add_validation_warning "Production environment without SSL is not recommended"
    fi
    
    # Summarize validation results
    if [[ $validation_errors -gt 0 ]]; then
        log_error "Configuration validation failed with $validation_errors error(s):"
        echo -e "$error_messages"
        
        if [[ $validation_warnings -gt 0 ]]; then
            log_warning "Additionally, $validation_warnings warning(s) were found:"
            echo -e "$warning_messages"
        fi
        
        if [[ "$INTERACTIVE" == true && "$DRY_RUN" != true ]]; then
            read -p "Configuration has errors. Do you want to continue anyway? (y/n): " continue_choice
            if [[ "$continue_choice" != "y" ]]; then
                log_error "Deployment aborted due to configuration errors"
                exit 1
            fi
            log_warning "Continuing deployment despite configuration errors"
        else
            log_error "Deployment aborted due to configuration errors"
            exit 1
        fi
    elif [[ $validation_warnings -gt 0 ]]; then
        log_warning "Configuration validation completed with $validation_warnings warning(s):"
        echo -e "$warning_messages"
        
        if [[ "$INTERACTIVE" == true && "$DRY_RUN" != true ]]; then
            read -p "Configuration has warnings. Do you want to continue? (y/n): " continue_choice
            if [[ "$continue_choice" != "y" ]]; then
                log_warning "Deployment aborted due to configuration warnings"
                exit 0
            fi
        fi
    else
        log_success "Configuration validation completed successfully"
    fi
    
    # Generate strong passwords and secrets if not provided
    if [[ -z "$DB_PASSWORD" ]]; then
        # Generate a stronger database password (32 characters with special chars)
        DB_PASSWORD=$(generate_strong_password 32)
        log_info "Generated strong random database password"
        log_debug "Password strength: High (32 chars, mixed case, numbers, special chars)"
    else
        # Validate provided password strength
        validate_password_strength "$DB_PASSWORD" "database"
    fi
    
    if [[ -z "$ADMIN_PASSWORD" ]]; then
        # Generate a stronger admin password (20 characters with special chars)
        ADMIN_PASSWORD=$(generate_strong_password 20)
        log_info "Generated strong random admin password: $ADMIN_PASSWORD"
        log_debug "Password strength: High (20 chars, mixed case, numbers, special chars)"
    else
        # Validate provided password strength
        validate_password_strength "$ADMIN_PASSWORD" "admin"
    fi
    
    if [[ -z "$JWT_SECRET" ]]; then
        # Generate a much stronger JWT secret (64 characters)
        JWT_SECRET=$(generate_strong_password 64)
        log_info "Generated strong random JWT secret"
        log_debug "Secret strength: Very High (64 chars, mixed case, numbers, special chars)"
    else
        # Validate provided JWT secret strength
        validate_password_strength "$JWT_SECRET" "JWT secret" 48
    fi
    
    log_success "Configuration processing completed"
}

interactive_configuration() {
    log_section "Interactive Configuration"
    
    # Domain configuration
    read -p "Enter domain name (e.g., hauling-system.example.com): " DOMAIN_NAME
    while [[ -z "$DOMAIN_NAME" ]]; do
        echo "Domain name cannot be empty."
        read -p "Enter domain name: " DOMAIN_NAME
    done
    
    # Environment configuration
    echo "Select environment mode:"
    echo "1) Production"
    echo "2) Staging"
    echo "3) Development"
    read -p "Enter selection [1]: " env_choice
    
    case "$env_choice" in
        1|"") ENV_MODE="production" ;;
        2) ENV_MODE="staging" ;;
        3) ENV_MODE="development" ;;
        *) echo "Invalid selection, using default (production)"; ENV_MODE="production" ;;
    esac
    
    # SSL configuration
    echo "Select SSL mode:"
    echo "1) No SSL"
    echo "2) Let's Encrypt (automatic certificates)"
    echo "3) Cloudflare (flexible SSL)"
    echo "4) Custom certificates"
    read -p "Enter selection [3]: " ssl_choice
    
    case "$ssl_choice" in
        1) SSL_MODE="none" ;;
        2) SSL_MODE="letsencrypt" ;;
        3|"") SSL_MODE="cloudflare" ;;
        4) SSL_MODE="custom" ;;
        *) echo "Invalid selection, using default (Cloudflare)"; SSL_MODE="cloudflare" ;;
    esac
    
    # If using Let's Encrypt, ask for email
    if [[ "$SSL_MODE" == "letsencrypt" ]]; then
        read -p "Enter email address for Let's Encrypt notifications: " ADMIN_EMAIL
        while [[ -z "$ADMIN_EMAIL" ]]; do
            echo "Email address cannot be empty for Let's Encrypt."
            read -p "Enter email address: " ADMIN_EMAIL
        done
    fi
    
    # If using custom SSL, ask for certificate paths
    if [[ "$SSL_MODE" == "custom" ]]; then
        read -p "Enter path to SSL certificate file: " SSL_CERT_PATH
        while [[ ! -f "$SSL_CERT_PATH" ]]; do
            echo "Certificate file not found."
            read -p "Enter path to SSL certificate file: " SSL_CERT_PATH
        done
        
        read -p "Enter path to SSL key file: " SSL_KEY_PATH
        while [[ ! -f "$SSL_KEY_PATH" ]]; do
            echo "Key file not found."
            read -p "Enter path to SSL key file: " SSL_KEY_PATH
        done
    fi
    
    # Admin user configuration
    read -p "Enter admin username [admin]: " admin_input
    ADMIN_USERNAME=${admin_input:-admin}
    
    read -p "Enter admin email: " ADMIN_EMAIL
    while [[ -z "$ADMIN_EMAIL" ]]; do
        echo "Admin email cannot be empty."
        read -p "Enter admin email: " ADMIN_EMAIL
    done
    
    read -s -p "Enter admin password (leave empty to generate): " admin_pass
    echo
    if [[ -n "$admin_pass" ]]; then
        ADMIN_PASSWORD=$admin_pass
    else
        ADMIN_PASSWORD=$(openssl rand -base64 12)
        echo "Generated admin password: $ADMIN_PASSWORD"
    fi
    
    # Database password
    read -s -p "Enter database password (leave empty to generate): " db_pass
    echo
    if [[ -n "$db_pass" ]]; then
        DB_PASSWORD=$db_pass
    else
        DB_PASSWORD=$(openssl rand -base64 16)
        echo "Generated database password (not shown for security)"
    fi
    
    # Repository configuration
    read -p "Enter repository URL [$REPO_URL]: " repo_input
    REPO_URL=${repo_input:-$REPO_URL}
    
    read -p "Enter repository branch [$REPO_BRANCH]: " branch_input
    REPO_BRANCH=${branch_input:-$REPO_BRANCH}
    
    # Monitoring configuration
    read -p "Enable system monitoring? (y/n) [y]: " monitoring_input
    if [[ "$monitoring_input" == "n" ]]; then
        MONITORING_ENABLED=false
    fi
    
    # Backup configuration
    read -p "Enable database backups? (y/n) [y]: " backup_input
    if [[ "$backup_input" == "n" ]]; then
        BACKUP_ENABLED=false
    else
        read -p "Enter backup retention days [$BACKUP_RETENTION_DAYS]: " retention_input
        BACKUP_RETENTION_DAYS=${retention_input:-$BACKUP_RETENTION_DAYS}
    fi
    
    log_success "Interactive configuration completed"
}

# ==============================
# Component Detection Functions
# ==============================

# Global variables to track component status
declare -A COMPONENT_STATUS
declare -A COMPONENT_VERSIONS
declare -A COMPONENT_REQUIRED_VERSIONS

# Initialize required versions
COMPONENT_REQUIRED_VERSIONS[nodejs]="18.0.0"
COMPONENT_REQUIRED_VERSIONS[npm]="8.0.0"
COMPONENT_REQUIRED_VERSIONS[nginx]="1.18.0"
COMPONENT_REQUIRED_VERSIONS[postgresql]="12.0"
COMPONENT_REQUIRED_VERSIONS[pm2]="5.0.0"

# Function to compare version numbers
version_compare() {
    local version1="$1"
    local version2="$2"
    
    # Remove 'v' prefix if present
    version1=${version1#v}
    version2=${version2#v}
    
    # Split versions into arrays
    IFS='.' read -ra V1 <<< "$version1"
    IFS='.' read -ra V2 <<< "$version2"
    
    # Compare each part
    for i in {0..2}; do
        local v1_part=${V1[i]:-0}
        local v2_part=${V2[i]:-0}
        
        # Remove non-numeric characters
        v1_part=$(echo "$v1_part" | sed 's/[^0-9].*$//')
        v2_part=$(echo "$v2_part" | sed 's/[^0-9].*$//')
        
        if [[ $v1_part -gt $v2_part ]]; then
            return 0  # version1 > version2
        elif [[ $v1_part -lt $v2_part ]]; then
            return 1  # version1 < version2
        fi
    done
    
    return 0  # versions are equal
}

# Function to detect Node.js installation and version
detect_nodejs() {
    log_debug "Detecting Node.js installation"
    
    if command -v node &> /dev/null; then
        local node_version=$(node --version 2>/dev/null | sed 's/v//')
        COMPONENT_VERSIONS[nodejs]="$node_version"
        
        if version_compare "$node_version" "${COMPONENT_REQUIRED_VERSIONS[nodejs]}"; then
            COMPONENT_STATUS[nodejs]="installed_compatible"
            log_info "Node.js detected: v$node_version (compatible)"
            return 0
        else
            COMPONENT_STATUS[nodejs]="installed_incompatible"
            log_warning "Node.js detected: v$node_version (incompatible, requires >= ${COMPONENT_REQUIRED_VERSIONS[nodejs]})"
            return 1
        fi
    else
        COMPONENT_STATUS[nodejs]="not_installed"
        log_info "Node.js not detected"
        return 1
    fi
}

# Function to detect NPM installation and version
detect_npm() {
    log_debug "Detecting NPM installation"
    
    if command -v npm &> /dev/null; then
        local npm_version=$(npm --version 2>/dev/null)
        COMPONENT_VERSIONS[npm]="$npm_version"
        
        if version_compare "$npm_version" "${COMPONENT_REQUIRED_VERSIONS[npm]}"; then
            COMPONENT_STATUS[npm]="installed_compatible"
            log_info "NPM detected: v$npm_version (compatible)"
            return 0
        else
            COMPONENT_STATUS[npm]="installed_incompatible"
            log_warning "NPM detected: v$npm_version (incompatible, requires >= ${COMPONENT_REQUIRED_VERSIONS[npm]})"
            return 1
        fi
    else
        COMPONENT_STATUS[npm]="not_installed"
        log_info "NPM not detected"
        return 1
    fi
}

# Function to detect Nginx installation and version
detect_nginx() {
    log_debug "Detecting Nginx installation"
    
    if command -v nginx &> /dev/null; then
        local nginx_version=$(nginx -v 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1)
        COMPONENT_VERSIONS[nginx]="$nginx_version"
        
        if version_compare "$nginx_version" "${COMPONENT_REQUIRED_VERSIONS[nginx]}"; then
            COMPONENT_STATUS[nginx]="installed_compatible"
            log_info "Nginx detected: v$nginx_version (compatible)"
            
            # Check if Nginx is running
            if systemctl is-active --quiet nginx; then
                log_info "Nginx service is running"
            else
                log_info "Nginx service is not running"
            fi
            return 0
        else
            COMPONENT_STATUS[nginx]="installed_incompatible"
            log_warning "Nginx detected: v$nginx_version (incompatible, requires >= ${COMPONENT_REQUIRED_VERSIONS[nginx]})"
            return 1
        fi
    else
        COMPONENT_STATUS[nginx]="not_installed"
        log_info "Nginx not detected"
        return 1
    fi
}

# Function to detect PostgreSQL installation and version
detect_postgresql() {
    log_debug "Detecting PostgreSQL installation"
    
    if command -v psql &> /dev/null; then
        local pg_version=$(psql --version 2>/dev/null | grep -o '[0-9]\+\.[0-9]\+' | head -1)
        COMPONENT_VERSIONS[postgresql]="$pg_version"
        
        if version_compare "$pg_version" "${COMPONENT_REQUIRED_VERSIONS[postgresql]}"; then
            COMPONENT_STATUS[postgresql]="installed_compatible"
            log_info "PostgreSQL detected: v$pg_version (compatible)"
            
            # Check if PostgreSQL is running
            if systemctl is-active --quiet postgresql; then
                log_info "PostgreSQL service is running"
            else
                log_info "PostgreSQL service is not running"
            fi
            return 0
        else
            COMPONENT_STATUS[postgresql]="installed_incompatible"
            log_warning "PostgreSQL detected: v$pg_version (incompatible, requires >= ${COMPONENT_REQUIRED_VERSIONS[postgresql]})"
            return 1
        fi
    else
        COMPONENT_STATUS[postgresql]="not_installed"
        log_info "PostgreSQL not detected"
        return 1
    fi
}

# Function to detect PM2 installation and version
detect_pm2() {
    log_debug "Detecting PM2 installation"
    
    if command -v pm2 &> /dev/null; then
        local pm2_version=$(pm2 --version 2>/dev/null)
        COMPONENT_VERSIONS[pm2]="$pm2_version"
        
        if version_compare "$pm2_version" "${COMPONENT_REQUIRED_VERSIONS[pm2]}"; then
            COMPONENT_STATUS[pm2]="installed_compatible"
            log_info "PM2 detected: v$pm2_version (compatible)"
            return 0
        else
            COMPONENT_STATUS[pm2]="installed_incompatible"
            log_warning "PM2 detected: v$pm2_version (incompatible, requires >= ${COMPONENT_REQUIRED_VERSIONS[pm2]})"
            return 1
        fi
    else
        COMPONENT_STATUS[pm2]="not_installed"
        log_info "PM2 not detected"
        return 1
    fi
}

# Function to detect all components
detect_all_components() {
    log_section "Component Detection"
    current_step="component_detection"
    
    log_info "Scanning system for existing components..."
    
    # Detect each component
    detect_nodejs
    detect_npm
    detect_nginx
    detect_postgresql
    detect_pm2
    
    # Generate component status report
    generate_component_status_report
    
    log_success "Component detection completed"
}

# Function to generate component details for deployment summary
generate_component_details_for_summary() {
    local components=("nodejs" "npm" "nginx" "postgresql" "pm2")
    local output=""
    
    for component in "${components[@]}"; do
        local status="${COMPONENT_STATUS[$component]}"
        local version="${COMPONENT_VERSIONS[$component]:-N/A}"
        local required="${COMPONENT_REQUIRED_VERSIONS[$component]}"
        
        case "$status" in
            "installed_compatible")
                output+="- **$component:** ✓ v$version (compatible, >= $required required)\n"
                ;;
            "installed_incompatible")
                output+="- **$component:** ⚠ v$version (incompatible, >= $required required)\n"
                ;;
            "not_installed")
                output+="- **$component:** ✗ Not installed (>= $required required)\n"
                ;;
            *)
                output+="- **$component:** ? Unknown status\n"
                ;;
        esac
    done
    
    echo -e "$output"
}

# Function to generate component status report
generate_component_status_report() {
    log_info "Component Status Summary:"
    log_info "========================"
    
    local components=("nodejs" "npm" "nginx" "postgresql" "pm2")
    local total_components=${#components[@]}
    local installed_count=0
    local compatible_count=0
    
    for component in "${components[@]}"; do
        local status="${COMPONENT_STATUS[$component]}"
        local version="${COMPONENT_VERSIONS[$component]:-N/A}"
        local required="${COMPONENT_REQUIRED_VERSIONS[$component]}"
        
        case "$status" in
            "installed_compatible")
                log_info "✓ $component: v$version (compatible, >= $required)"
                ((installed_count++))
                ((compatible_count++))
                ;;
            "installed_incompatible")
                log_warning "⚠ $component: v$version (incompatible, requires >= $required)"
                ((installed_count++))
                ;;
            "not_installed")
                log_info "✗ $component: Not installed (requires >= $required)"
                ;;
            *)
                log_warning "? $component: Unknown status"
                ;;
        esac
    done
    
    log_info "========================"
    log_info "Summary: $compatible_count/$total_components components compatible, $installed_count/$total_components installed"
    
    # Store summary for deployment report
    COMPONENT_DETECTION_SUMMARY="$compatible_count/$total_components components compatible, $installed_count/$total_components installed"
}

# Function to install Node.js with version checking
install_nodejs_if_needed() {
    local component="nodejs"
    
    if [[ "${COMPONENT_STATUS[$component]}" == "installed_compatible" ]]; then
        log_info "Skipping Node.js installation - compatible version already installed (v${COMPONENT_VERSIONS[$component]})"
        return 0
    fi
    
    log_step "Installing Node.js"
    current_step="nodejs_installation"
    
    if [[ "${COMPONENT_STATUS[$component]}" == "installed_incompatible" ]]; then
        log_warning "Upgrading incompatible Node.js version (v${COMPONENT_VERSIONS[$component]} -> >= ${COMPONENT_REQUIRED_VERSIONS[$component]})"
    fi
    
    # Install Node.js from NodeSource repository for latest LTS
    log_info "Adding NodeSource repository for Node.js LTS"
    curl -fsSL https://deb.nodesource.com/setup_lts.x | bash -
    
    log_info "Installing Node.js from NodeSource repository"
    apt-get install -y nodejs
    
    # Verify installation
    if detect_nodejs; then
        log_success "Node.js installation completed successfully (v${COMPONENT_VERSIONS[$component]})"
    else
        log_error "Node.js installation failed or version is still incompatible"
        return 1
    fi
}

# Function to install NPM if needed (usually comes with Node.js)
install_npm_if_needed() {
    local component="npm"
    
    if [[ "${COMPONENT_STATUS[$component]}" == "installed_compatible" ]]; then
        log_info "Skipping NPM installation - compatible version already installed (v${COMPONENT_VERSIONS[$component]})"
        return 0
    fi
    
    log_step "Installing/Updating NPM"
    current_step="npm_installation"
    
    # NPM usually comes with Node.js, but we might need to update it
    log_info "Updating NPM to latest version"
    npm install -g npm@latest
    
    # Verify installation
    if detect_npm; then
        log_success "NPM installation/update completed successfully (v${COMPONENT_VERSIONS[$component]})"
    else
        log_error "NPM installation/update failed or version is still incompatible"
        return 1
    fi
}

# Function to install Nginx if needed
install_nginx_if_needed() {
    local component="nginx"
    
    if [[ "${COMPONENT_STATUS[$component]}" == "installed_compatible" ]]; then
        log_info "Skipping Nginx installation - compatible version already installed (v${COMPONENT_VERSIONS[$component]})"
        
        # Check if Nginx is running and start if needed
        if ! service_is_active nginx; then
            log_info "Starting Nginx service"
            service_start nginx
            service_enable nginx
        fi
        return 0
    fi
    
    log_step "Installing Nginx"
    current_step="nginx_installation"
    
    if [[ "${COMPONENT_STATUS[$component]}" == "installed_incompatible" ]]; then
        log_warning "Upgrading incompatible Nginx version (v${COMPONENT_VERSIONS[$component]} -> >= ${COMPONENT_REQUIRED_VERSIONS[$component]})"
    fi
    
    log_info "Installing Nginx web server"
    apt-get install -y nginx
    
    # Enable and start Nginx
    service_enable nginx
    service_start nginx
    
    # Verify installation
    if detect_nginx; then
        log_success "Nginx installation completed successfully (v${COMPONENT_VERSIONS[$component]})"
    else
        log_error "Nginx installation failed or version is still incompatible"
        return 1
    fi
}

# Function to install PostgreSQL if needed
install_postgresql_if_needed() {
    local component="postgresql"
    
    if [[ "${COMPONENT_STATUS[$component]}" == "installed_compatible" ]]; then
        log_info "Skipping PostgreSQL installation - compatible version already installed (v${COMPONENT_VERSIONS[$component]})"
        
        # Check if PostgreSQL is running and start if needed
        if ! service_is_active postgresql; then
            log_info "Starting PostgreSQL service"
            service_start postgresql
            service_enable postgresql
        fi
        return 0
    fi
    
    log_step "Installing PostgreSQL"
    current_step="postgresql_installation"
    
    if [[ "${COMPONENT_STATUS[$component]}" == "installed_incompatible" ]]; then
        log_warning "Upgrading incompatible PostgreSQL version (v${COMPONENT_VERSIONS[$component]} -> >= ${COMPONENT_REQUIRED_VERSIONS[$component]})"
    fi
    
    log_info "Installing PostgreSQL database server"
    apt-get install -y postgresql postgresql-contrib
    
    # Enable and start PostgreSQL
    service_enable postgresql
    service_start postgresql
    
    # Verify installation
    if detect_postgresql; then
        log_success "PostgreSQL installation completed successfully (v${COMPONENT_VERSIONS[$component]})"
    else
        log_error "PostgreSQL installation failed or version is still incompatible"
        return 1
    fi
}

# Function to install PM2 if needed
install_pm2_if_needed() {
    local component="pm2"
    
    if [[ "${COMPONENT_STATUS[$component]}" == "installed_compatible" ]]; then
        log_info "Skipping PM2 installation - compatible version already installed (v${COMPONENT_VERSIONS[$component]})"
        return 0
    fi
    
    log_step "Installing PM2 Process Manager"
    current_step="pm2_installation"
    
    if [[ "${COMPONENT_STATUS[$component]}" == "installed_incompatible" ]]; then
        log_warning "Upgrading incompatible PM2 version (v${COMPONENT_VERSIONS[$component]} -> >= ${COMPONENT_REQUIRED_VERSIONS[$component]})"
    fi
    
    log_info "Installing PM2 globally via NPM"
    npm install -g pm2
    
    # Verify installation
    if detect_pm2; then
        log_success "PM2 installation completed successfully (v${COMPONENT_VERSIONS[$component]})"
    else
        log_error "PM2 installation failed or version is still incompatible"
        return 1
    fi
}

# Function to install other dependencies if needed
install_other_dependencies_if_needed() {
    log_step "Installing other required dependencies"
    current_step="other_dependencies_installation"
    
    local dependencies=("curl" "git" "certbot" "python3-certbot-nginx" "fail2ban" "ufw" "logrotate" "unzip")
    local missing_deps=()
    
    # Check which dependencies are missing
    for dep in "${dependencies[@]}"; do
        if ! command -v "$dep" &> /dev/null && ! dpkg -l | grep -q "^ii  $dep "; then
            missing_deps+=("$dep")
        fi
    done
    
    if [[ ${#missing_deps[@]} -eq 0 ]]; then
        log_info "All other dependencies are already installed"
        return 0
    fi
    
    log_info "Installing missing dependencies: ${missing_deps[*]}"
    apt-get install -y "${missing_deps[@]}"
    
    log_success "Other dependencies installation completed"
}

# ==============================
# Deployment Functions
# ==============================

prepare_system() {
    log_section "System Preparation"

    # Detect environment and init system first
    detect_environment

    # Handle Ubuntu version differences
    handle_ubuntu_version_differences

    # Detect existing components
    detect_all_components

    # Update package lists and upgrade system
    log_step "Updating system packages"
    current_step="system_update"
    apt-get update
    apt-get upgrade -y

    # Install components using enhanced setup
    log_step "Installing required components"

    # Setup Node.js ecosystem comprehensively
    if ! setup_nodejs_ecosystem; then
        log_error "Failed to setup Node.js ecosystem"
        return 1
    fi

    # Install Nginx if needed
    install_nginx_if_needed

    # Install PostgreSQL if needed
    install_postgresql_if_needed

    # Install other dependencies if needed
    install_other_dependencies_if_needed
    
    # Create application user if it doesn't exist
    log_step "Creating application user"
    current_step="user_creation"
    if id "hauling_app" &>/dev/null; then
        log_info "Application user 'hauling_app' already exists - skipping creation"
    else
        log_info "Creating application user 'hauling_app'"
        useradd -m -s /bin/bash hauling_app
        log_success "Application user created successfully"
    fi
    
    # Create application directories if they don't exist
    log_step "Creating application directories"
    current_step="directory_setup"
    if [[ -d "/var/www/hauling-qr-system" ]]; then
        log_info "Application directory '/var/www/hauling-qr-system' already exists - skipping creation"
    else
        log_info "Creating application directory '/var/www/hauling-qr-system'"
        mkdir -p /var/www/hauling-qr-system
        log_success "Application directory created successfully"
    fi
    
    # Ensure proper ownership regardless of whether directory existed
    chown hauling_app:hauling_app /var/www/hauling-qr-system
    
    # Re-detect components after installation to update status
    log_step "Verifying component installations"
    detect_all_components
    
    log_success "System preparation completed"
}

setup_security() {
    log_section "Security Hardening"
    
    # Configure firewall with standard security
    log_step "Configuring firewall"
    current_step="firewall_setup"
    
    # Set default policies
    log_info "Setting default firewall policies"
    ufw default deny incoming
    ufw default allow outgoing
    
    # Allow essential services
    log_info "Configuring firewall rules for essential services"
    
    # SSH access (standard, without rate limiting)
    ufw allow ssh comment "Allow SSH"
    
    # HTTP and HTTPS for web traffic
    ufw allow http comment "Allow HTTP"
    ufw allow https comment "Allow HTTPS"
    
    # Allow access to the backend API port
    # Note: In a production environment, this should ideally be restricted,
    # but we're keeping it open to avoid potential connectivity issues
    ufw allow 5000/tcp comment "Allow backend API port"
    
    # Enable the firewall
    log_info "Enabling firewall"
    ufw --force enable
    
    # Show firewall status
    ufw status
    
    # Configure Fail2Ban with standard settings
    log_step "Configuring Fail2Ban"
    current_step="fail2ban_setup"
    cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
    cat > /etc/fail2ban/jail.d/custom.conf << EOF
[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
# More permissive settings
maxretry = 8
bantime = 1800
findtime = 600
EOF
    systemctl enable fail2ban
    systemctl restart fail2ban
    
    log_success "Security hardening completed"
}

deploy_application() {
    log_section "Application Deployment"

    # Setup application directories first
    if ! setup_application_directories "/var/www/hauling-qr-system" "hauling_app"; then
        log_error "Failed to setup application directories"
        return 1
    fi

    # Clone repository safely
    log_step "Cloning repository"
    current_step="repository_clone"
    if ! clone_repository_safe "$REPO_URL" "/var/www/hauling-qr-system" "$REPO_BRANCH" true; then
        log_error "Failed to clone repository"
        return 1
    fi

    # Validate working directory
    if ! validate_working_directory "/var/www/hauling-qr-system"; then
        log_error "Failed to validate working directory"
        return 1
    fi

    # Install backend dependencies
    log_step "Installing backend dependencies"
    current_step="npm_install"

    # Install root dependencies
    if ! npm install; then
        log_error "Failed to install root dependencies"
        return 1
    fi

    # Install server dependencies
    if ! safe_cd "server"; then
        log_error "Failed to navigate to server directory"
        return 1
    fi

    if ! npm install; then
        log_error "Failed to install server dependencies"
        return 1
    fi

    # Return to root directory
    if ! safe_cd ".."; then
        log_error "Failed to return to root directory"
        return 1
    fi

    # Install frontend dependencies and build
    log_step "Building frontend"
    current_step="frontend_build"

    if ! safe_cd "client"; then
        log_error "Failed to navigate to client directory"
        return 1
    fi

    if ! npm install; then
        log_error "Failed to install client dependencies"
        return 1
    fi

    if ! npm run build; then
        log_error "Failed to build frontend"
        return 1
    fi

    # Return to root directory
    if ! safe_cd ".."; then
        log_error "Failed to return to root directory after build"
        return 1
    fi
    
    # Create environment configuration
    log_step "Creating environment configuration"
    current_step="environment_config"
    cat > .env << EOF
# ============================================================================
# HAULING QR TRIP SYSTEM - ENVIRONMENT CONFIGURATION
# ============================================================================
# Domain: $DOMAIN_NAME
# Environment: $ENV_MODE
# Generated: $(date '+%Y-%m-%d %H:%M:%S')
# ============================================================================

# Environment Configuration
NODE_ENV=$ENV_MODE
PORT=5000
HOST=0.0.0.0
ENABLE_HTTPS=$([ "$SSL_MODE" != "none" ] && echo "true" || echo "false")

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=hauling_app
DB_PASSWORD=$DB_PASSWORD
DB_POOL_MAX=25
DB_POOL_MIN=5
DB_SSL_ENABLED=true
DB_CONNECTION_TIMEOUT=10000
DB_IDLE_TIMEOUT=30000

# JWT Configuration
JWT_SECRET=$JWT_SECRET
JWT_EXPIRATION=24h
JWT_REFRESH_SECRET=$(generate_strong_password 64)
JWT_REFRESH_EXPIRATION=7d
JWT_ALGORITHM=HS512
JWT_ISSUER=$DOMAIN_NAME
JWT_AUDIENCE=hauling-qr-system

# API Configuration
API_URL=https://$DOMAIN_NAME/api
WS_URL=wss://$DOMAIN_NAME/ws
API_TIMEOUT=30000
API_MAX_PAYLOAD_SIZE=10mb

# Client Configuration
REACT_APP_API_URL=https://$DOMAIN_NAME/api
REACT_APP_WS_URL=wss://$DOMAIN_NAME/ws
REACT_APP_USE_HTTPS=true

# CORS Configuration
CORS_ORIGIN=https://$DOMAIN_NAME
ALLOWED_ORIGINS=https://$DOMAIN_NAME,https://www.$DOMAIN_NAME
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With
CORS_EXPOSE_HEADERS=Content-Length,X-Request-ID
CORS_MAX_AGE=86400
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_TRUSTED=true
RATE_LIMIT_HEADERS=true
RATE_LIMIT_LEGACY_HEADERS=false
RATE_LIMIT_AUTH_WINDOW_MS=300000
RATE_LIMIT_AUTH_MAX_REQUESTS=5

# Security Configuration
HELMET_ENABLED=true
CONTENT_SECURITY_POLICY="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'"
XSS_PROTECTION=true
FRAME_OPTIONS=SAMEORIGIN
CONTENT_TYPE_OPTIONS=nosniff
REFERRER_POLICY=strict-origin-when-cross-origin
COOKIE_SECURE=true
COOKIE_HTTP_ONLY=true
COOKIE_SAME_SITE=Strict
PASSWORD_HASH_ROUNDS=12
SESSION_SECRET=$(generate_strong_password 48)

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_FILE_PATH=/var/www/hauling-qr-system/logs/app.log
LOG_FORMAT=combined
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5
LOG_COLORIZE=false
LOG_TIMESTAMP=true
LOG_SANITIZE=true

# QR Code Configuration
QR_CODE_SIZE=200
QR_CODE_QUALITY=H
QR_CODE_ERROR_CORRECTION=H
QR_CODE_MARGIN=4
REACT_APP_QR_CAMERA_FACING=environment
REACT_APP_QR_SCAN_DELAY=500

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=/var/www/hauling-qr-system/uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf
SCAN_UPLOADS=true
SANITIZE_FILENAMES=true
MAX_FILES_PER_REQUEST=10
EOF
    
    # Set proper ownership
    chown -R hauling_app:hauling_app /var/www/hauling-qr-system
    
    # Set secure file permissions
    log_step "Setting secure file permissions"
    current_step="file_permissions"
    
    # Set base permissions
    find /var/www/hauling-qr-system -type d -exec chmod 750 {} \;
    find /var/www/hauling-qr-system -type f -exec chmod 640 {} \;
    
    # Set more restrictive permissions for sensitive files
    log_info "Setting enhanced security permissions for sensitive files"
    
    # Environment file with credentials - highly sensitive (600 = owner read/write only)
    if [[ -f "/var/www/hauling-qr-system/.env" ]]; then
        chmod 600 /var/www/hauling-qr-system/.env
        log_success "Enhanced permissions set for .env file (600)"
    fi
    
    # Configuration files with potential secrets
    find /var/www/hauling-qr-system -name "config*.js" -exec chmod 600 {} \;
    find /var/www/hauling-qr-system -name "*.config.js" -exec chmod 600 {} \;
    find /var/www/hauling-qr-system -name "*.env*" -exec chmod 600 {} \;
    log_success "Enhanced permissions set for configuration files (600)"
    
    # SSL certificates - private keys are highly sensitive
    if [[ -d "/etc/nginx/ssl" ]]; then
        chmod 700 /etc/nginx/ssl  # Only owner can access directory
        find /etc/nginx/ssl -name "*.key" -exec chmod 600 {} \;  # Private keys: owner read/write only
        find /etc/nginx/ssl -name "*.crt" -exec chmod 644 {} \;  # Public certs: owner rw, group/others read
        log_success "Enhanced permissions set for SSL directory and certificates"
    fi
    
    # Database backup directory - contains sensitive data
    if [[ -d "/var/backups/hauling-qr-system" ]]; then
        chmod 700 /var/backups/hauling-qr-system  # Only owner can access directory
        find /var/backups/hauling-qr-system -type f -exec chmod 600 {} \;  # Only owner can read backups
        log_success "Enhanced permissions set for database backups"
    fi
    
    # Log files - may contain sensitive information
    if [[ -d "/var/www/hauling-qr-system/server/logs" ]]; then
        chmod 750 /var/www/hauling-qr-system/server/logs  # Owner: rwx, Group: rx, Others: none
        find /var/www/hauling-qr-system/server/logs -type f -exec chmod 640 {} \;  # Owner: rw, Group: r, Others: none
        log_success "Enhanced permissions set for log files"
    fi
    
    # Executable scripts - need execute permission
    find /var/www/hauling-qr-system -name "*.sh" -exec chmod 750 {} \;  # Owner: rwx, Group: rx, Others: none
    
    # Uploads directory - needs write permission for app user
    if [[ -d "/var/www/hauling-qr-system/uploads" ]]; then
        chmod 750 /var/www/hauling-qr-system/uploads  # Owner: rwx, Group: rx, Others: none
        find /var/www/hauling-qr-system/uploads -type f -exec chmod 640 {} \;  # Owner: rw, Group: r, Others: none
        log_success "Enhanced permissions set for uploads directory"
    fi
    
    # Set proper ownership for all files
    chown -R hauling_app:hauling_app /var/www/hauling-qr-system
    
    # Set proper ownership for Nginx configuration
    if [[ -f "/etc/nginx/sites-available/hauling-qr-system" ]]; then
        chown root:root /etc/nginx/sites-available/hauling-qr-system
        chmod 644 /etc/nginx/sites-available/hauling-qr-system
        log_success "Enhanced permissions set for Nginx configuration"
    fi
    
    # Set proper ownership for SSL directory
    if [[ -d "/etc/nginx/ssl" ]]; then
        chown -R root:root /etc/nginx/ssl
        log_success "Enhanced ownership set for SSL directory"
    fi
    
    # Set proper permissions for deployment logs
    if [[ -d "/var/log/hauling-deployment" ]]; then
        chmod 750 /var/log/hauling-deployment
        find /var/log/hauling-deployment -type f -exec chmod 640 {} \;
        log_success "Enhanced permissions set for deployment logs"
    fi
    
    # Set proper permissions for health check script
    if [[ -f "/usr/local/bin/hauling-health-check.sh" ]]; then
        chmod 700 /usr/local/bin/hauling-health-check.sh
        chown root:root /usr/local/bin/hauling-health-check.sh
        log_success "Enhanced permissions set for health check script"
    fi
    
    # Set proper permissions for backup script
    if [[ -f "/usr/local/bin/backup-hauling-db.sh" ]]; then
        chmod 700 /usr/local/bin/backup-hauling-db.sh
        chown root:root /usr/local/bin/backup-hauling-db.sh
        log_success "Enhanced permissions set for backup script"
    fi
    
    # Verify permissions for critical files
    log_debug "Verifying permissions for sensitive files"
    ls -la /var/www/hauling-qr-system/.env 2>/dev/null || log_debug ".env file not found"
    ls -la /etc/nginx/ssl 2>/dev/null || log_debug "SSL directory not found"
    
    # Perform security audit of file permissions
    log_info "Performing security audit of file permissions"
    
    # Check for world-writable files (security risk)
    world_writable=$(find /var/www/hauling-qr-system -type f -perm -002 | wc -l)
    if [[ $world_writable -gt 0 ]]; then
        log_warning "Found $world_writable world-writable files. This is a security risk."
        log_debug "World-writable files: $(find /var/www/hauling-qr-system -type f -perm -002 | head -n 5)"
        # Fix world-writable files
        find /var/www/hauling-qr-system -type f -perm -002 -exec chmod o-w {} \;
        log_success "Fixed world-writable file permissions"
    else
        log_success "No world-writable files found"
    fi
    
    # Check for world-writable directories (security risk)
    world_writable_dirs=$(find /var/www/hauling-qr-system -type d -perm -002 | wc -l)
    if [[ $world_writable_dirs -gt 0 ]]; then
        log_warning "Found $world_writable_dirs world-writable directories. This is a security risk."
        log_debug "World-writable directories: $(find /var/www/hauling-qr-system -type d -perm -002 | head -n 5)"
        # Fix world-writable directories
        find /var/www/hauling-qr-system -type d -perm -002 -exec chmod o-w {} \;
        log_success "Fixed world-writable directory permissions"
    else
        log_success "No world-writable directories found"
    fi
    
    log_success "Enhanced secure file permissions applied"
    
    log_success "Application deployment completed"
}

setup_database() {
    log_section "Database Setup"
    current_step="database_setup"
    
    # Create database user
    log_step "Creating database user"
    sudo -u postgres psql -c "CREATE USER hauling_app WITH PASSWORD '$DB_PASSWORD';"
    
    # Create database
    log_step "Creating database"
    sudo -u postgres psql -c "CREATE DATABASE hauling_qr_system OWNER hauling_app;"
    
    # Initialize database schema
    log_step "Initializing database schema"
    cd /var/www/hauling-qr-system
    sudo -u hauling_app psql -U hauling_app -d hauling_qr_system -f database/init.sql
    
    # Run migrations if needed
    log_step "Running database migrations"
    cd /var/www/hauling-qr-system
    sudo -u hauling_app node database/run-migration.js
    
    # Create admin user
    log_step "Creating admin user"
    cd /var/www/hauling-qr-system
    sudo -u hauling_app node server/create-admin-user.js \
        --username "$ADMIN_USERNAME" \
        --password "$ADMIN_PASSWORD" \
        --email "$ADMIN_EMAIL"
    
    log_success "Database setup completed"
}

configure_infrastructure() {
    log_section "Infrastructure Configuration"
    
    # Configure Nginx
    log_step "Configuring Nginx"
    current_step="nginx_config"
    cat > /etc/nginx/sites-available/hauling-qr-system << EOF
# Cloudflare IP ranges for real IP detection
set_real_ip_from ************/22;
set_real_ip_from ************/22;
set_real_ip_from **********/22;
set_real_ip_from **********/13;
set_real_ip_from **********/14;
set_real_ip_from *************/18;
set_real_ip_from **********/22;
set_real_ip_from ************/18;
set_real_ip_from ***********/15;
set_real_ip_from **********/13;
set_real_ip_from ************/20;
set_real_ip_from ************/20;
set_real_ip_from ************/20;
set_real_ip_from *************/22;
set_real_ip_from ************/17;
set_real_ip_from 2400:cb00::/32;
set_real_ip_from 2606:4700::/32;
set_real_ip_from 2803:f800::/32;
set_real_ip_from 2405:b500::/32;
set_real_ip_from 2405:8100::/32;
set_real_ip_from 2c0f:f248::/32;
set_real_ip_from 2a06:98c0::/29;
real_ip_header CF-Connecting-IP;

server {
    listen 80;
    server_name $DOMAIN_NAME www.$DOMAIN_NAME;
    
    # Basic security headers (standard level)
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "same-origin" always;
    # More permissive CSP that allows common third-party resources
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data: https:; connect-src 'self' https:;" always;
    
    # Disable server tokens for basic security
    server_tokens off;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript application/json application/javascript application/xml+rss application/atom+xml image/svg+xml;
    
    # Standard rate limiting (less restrictive)
    # API-specific rate limiting with generous limits
    limit_req_zone \$binary_remote_addr zone=api:10m rate=20r/s;
    
    # Authentication endpoints with moderate limits
    limit_req_zone \$binary_remote_addr zone=auth:10m rate=5r/s;
    
    # Frontend static files
    location / {
        root /var/www/hauling-qr-system/client/build;
        try_files \$uri \$uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Backend API with standard security
    location /api {
        # Apply moderate API rate limiting
        limit_req zone=api burst=30 nodelay;
        
        # Proxy configuration
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Standard timeout settings
        proxy_read_timeout 300s;
        client_max_body_size 15m;  # More permissive file size limit
        
        # Basic security headers
        add_header X-Content-Type-Options "nosniff" always;
    }
    
    # WebSocket endpoint with standard security
    location /ws {
        # No rate limiting for WebSockets to avoid connection issues
        
        # Proxy configuration
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "Upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # WebSocket specific settings
        proxy_read_timeout 3600s;  # Allow long-lived connections
        proxy_buffering off;       # Disable buffering for real-time data
    }
    
    # Authentication endpoints with moderate rate limiting
    location ~ ^/api/auth/(login|register|reset-password) {
        # Apply moderate rate limiting for auth endpoints
        limit_req zone=auth burst=10 nodelay;
        
        # Proxy configuration
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Standard timeout settings
        proxy_read_timeout 120s;
        client_max_body_size 2m;
        
        # Basic security headers
        add_header X-Content-Type-Options "nosniff" always;
        add_header Cache-Control "no-store" always;
    }
    
    # Protect sensitive files but allow necessary access
    location ~ \.(env|git|htaccess|htpasswd)$ {
        deny all;
        return 404;
    }
}
EOF
    
    # Enable site
    ln -sf /etc/nginx/sites-available/hauling-qr-system /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
    
    # Configure SSL based on selected mode
    log_step "Configuring SSL"
    current_step="ssl_setup"
    if [[ "$SSL_MODE" == "letsencrypt" ]]; then
        log_step "Configuring Let's Encrypt SSL"
        certbot --nginx -d "$DOMAIN_NAME" --non-interactive --agree-tos --email "$ADMIN_EMAIL"
    elif [[ "$SSL_MODE" == "custom" ]]; then
        log_step "Configuring custom SSL certificates"
        # Copy custom certificates and update Nginx config
        mkdir -p /etc/nginx/ssl
        cp "$SSL_CERT_PATH" /etc/nginx/ssl/certificate.crt
        cp "$SSL_KEY_PATH" /etc/nginx/ssl/private.key
        
        # Update Nginx config for SSL
        sed -i "s/listen 80;/listen 80;\n    listen 443 ssl;/" /etc/nginx/sites-available/hauling-qr-system
        sed -i "/server_name/a \    ssl_certificate /etc/nginx/ssl/certificate.crt;\n    ssl_certificate_key /etc/nginx/ssl/private.key;\n    ssl_protocols TLSv1.2 TLSv1.3;\n    ssl_prefer_server_ciphers on;\n    ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';" /etc/nginx/sites-available/hauling-qr-system
    elif [[ "$SSL_MODE" == "cloudflare" ]]; then
        log_step "Configuring for Cloudflare Full SSL"
        # Create self-signed certificate for Cloudflare Full SSL mode
        log_info "Generating self-signed SSL certificate for $DOMAIN_NAME (Cloudflare Full SSL mode)"
        mkdir -p /etc/nginx/ssl
        openssl req -x509 -nodes -days 730 -newkey rsa:2048 \
            -keyout /etc/nginx/ssl/private.key \
            -out /etc/nginx/ssl/certificate.crt \
            -subj "/C=US/ST=State/L=City/O=Hauling QR System/CN=$DOMAIN_NAME" \
            -addext "subjectAltName=DNS:$DOMAIN_NAME,DNS:www.$DOMAIN_NAME"
        chmod 600 /etc/nginx/ssl/private.key
        chmod 644 /etc/nginx/ssl/certificate.crt
        
        # Update Nginx config for SSL
        sed -i "s/listen 80;/listen 80;\n    listen 443 ssl;/" /etc/nginx/sites-available/hauling-qr-system
        sed -i "/server_name/a \    ssl_certificate /etc/nginx/ssl/certificate.crt;\n    ssl_certificate_key /etc/nginx/ssl/private.key;\n    ssl_protocols TLSv1.2 TLSv1.3;\n    ssl_prefer_server_ciphers on;\n    ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';" /etc/nginx/sites-available/hauling-qr-system
        
        # Add Cloudflare IP detection and security headers
        cat > /tmp/cloudflare-ips << 'EOF'
# Cloudflare IP ranges for real IP detection
set_real_ip_from ************/22;
set_real_ip_from ************/22;
set_real_ip_from **********/22;
set_real_ip_from **********/13;
set_real_ip_from **********/14;
set_real_ip_from *************/18;
set_real_ip_from **********/22;
set_real_ip_from ************/18;
set_real_ip_from ***********/15;
set_real_ip_from **********/13;
set_real_ip_from ************/20;
set_real_ip_from ************/20;
set_real_ip_from ************/20;
set_real_ip_from *************/22;
set_real_ip_from ************/17;
set_real_ip_from 2400:cb00::/32;
set_real_ip_from 2606:4700::/32;
set_real_ip_from 2803:f800::/32;
set_real_ip_from 2405:b500::/32;
set_real_ip_from 2405:8100::/32;
set_real_ip_from 2c0f:f248::/32;
set_real_ip_from 2a06:98c0::/29;
real_ip_header CF-Connecting-IP;
EOF
        cat /tmp/cloudflare-ips | cat - /etc/nginx/sites-available/hauling-qr-system > /tmp/nginx-config
        mv /tmp/nginx-config /etc/nginx/sites-available/hauling-qr-system
        
        # Add security headers
        sed -i "/add_header/a \    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains\" always;" /etc/nginx/sites-available/hauling-qr-system
    fi
    
    # Test Nginx configuration
    nginx -t
    
    # Reload Nginx
    service_stop nginx && service_start nginx
    
    # Configure PM2 for process management
    log_step "Configuring PM2 process management"
    current_step="pm2_setup"
    cd /var/www/hauling-qr-system
    
    # Create PM2 ecosystem file
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'hauling-qr-api',
    script: 'server/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: '$ENV_MODE',
    }
  }]
};
EOF
    
    # Start application with PM2
    sudo -u hauling_app pm2 start ecosystem.config.js
    
    # Save PM2 configuration to start on system boot
    sudo -u hauling_app pm2 save
    pm2 startup systemd -u hauling_app --hp /home/<USER>
    systemctl enable pm2-hauling_app
    
    log_success "Infrastructure configuration completed"
}

setup_monitoring() {
    log_section "Monitoring & Maintenance"
    
    # Configure log rotation
    log_step "Configuring log rotation"
    cat > /etc/logrotate.d/hauling-qr-system << EOF
/var/www/hauling-qr-system/server/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 hauling_app hauling_app
    sharedscripts
    postrotate
        systemctl reload pm2-hauling_app > /dev/null 2>/dev/null || true
    endscript
}
EOF
    
    # Set up database backups
    if [[ "$BACKUP_ENABLED" == true ]]; then
        log_step "Setting up database backups"
        current_step="backup_setup"
        mkdir -p /var/backups/hauling-qr-system
        chown hauling_app:hauling_app /var/backups/hauling-qr-system
        
        # Create backup script
        cat > /usr/local/bin/backup-hauling-db.sh << EOF
#!/bin/bash
BACKUP_DIR="/var/backups/hauling-qr-system"
TIMESTAMP=\$(date +"%Y%m%d-%H%M%S")
BACKUP_FILE="\$BACKUP_DIR/hauling_qr_system-\$TIMESTAMP.sql"
RETENTION_DAYS=$BACKUP_RETENTION_DAYS

# Create backup
sudo -u postgres pg_dump hauling_qr_system > "\$BACKUP_FILE"
gzip "\$BACKUP_FILE"

# Remove old backups
find "\$BACKUP_DIR" -name "hauling_qr_system-*.sql.gz" -type f -mtime +\$RETENTION_DAYS -delete
EOF
        
        chmod +x /usr/local/bin/backup-hauling-db.sh
        
        # Set up cron job for daily backups
        echo "0 2 * * * root /usr/local/bin/backup-hauling-db.sh > /dev/null 2>&1" > /etc/cron.d/hauling-db-backup
    fi
    
    # Set up health checks
    log_step "Setting up health checks"
    current_step="health_check_setup"
    cat > /usr/local/bin/hauling-health-check.sh << EOF
#!/bin/bash
API_URL="http://localhost:5000/api/health"
LOG_FILE="/var/log/hauling-health-check.log"

# Check API health
response=\$(curl -s -o /dev/null -w "%{http_code}" \$API_URL)

if [[ "\$response" != "200" ]]; then
    echo "\$(date): Health check failed with status \$response" >> \$LOG_FILE
    # Attempt to restart the service
    sudo -u hauling_app pm2 restart hauling-qr-api
    echo "\$(date): Restarted service after failed health check" >> \$LOG_FILE
else
    echo "\$(date): Health check passed" >> \$LOG_FILE
fi
EOF
    
    chmod +x /usr/local/bin/hauling-health-check.sh
    
    # Set up cron job for health checks
    echo "*/5 * * * * root /usr/local/bin/hauling-health-check.sh > /dev/null 2>&1" > /etc/cron.d/hauling-health-check
    
    log_success "Monitoring and maintenance setup completed"
}

verify_deployment() {
    log_section "Deployment Verification"
    current_step="deployment_verification"
    
    # Create verification results array
    declare -A verification_results
    verification_passed=true
    verification_warnings=0
    
    # Check system requirements
    log_step "Verifying system requirements"
    
    # Check CPU cores
    cpu_cores=$(nproc)
    if [[ $cpu_cores -lt 1 ]]; then
        log_warning "System has only $cpu_cores CPU cores. Recommended: at least 1 core."
        verification_results["cpu_cores"]="WARNING"
        verification_warnings=$((verification_warnings + 1))
    else
        log_success "CPU cores: $cpu_cores"
        verification_results["cpu_cores"]="PASS"
    fi
    
    # Check memory
    total_memory_kb=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    total_memory_mb=$((total_memory_kb / 1024))
    if [[ $total_memory_mb -lt 2048 ]]; then
        log_warning "System has only $total_memory_mb MB memory. Recommended: at least 2048 MB."
        verification_results["memory"]="WARNING"
        verification_warnings=$((verification_warnings + 1))
    else
        log_success "Memory: $total_memory_mb MB"
        verification_results["memory"]="PASS"
    fi
    
    # Check disk space
    root_disk_free=$(df -m / | awk 'NR==2 {print $4}')
    if [[ $root_disk_free -lt 5120 ]]; then
        log_warning "System has only $root_disk_free MB free disk space. Recommended: at least 5120 MB."
        verification_results["disk_space"]="WARNING"
        verification_warnings=$((verification_warnings + 1))
    else
        log_success "Free disk space: $root_disk_free MB"
        verification_results["disk_space"]="PASS"
    fi
    
    # Check required services
    log_step "Verifying required services"
    
    # Check Nginx status
    if service_is_active nginx; then
        log_success "Nginx is running"
        verification_results["nginx_status"]="PASS"
        
        # Check Nginx configuration
        if nginx -t &>/dev/null; then
            log_success "Nginx configuration is valid"
            verification_results["nginx_config"]="PASS"
        else
            log_error "Nginx configuration is invalid"
            verification_results["nginx_config"]="FAIL"
            verification_passed=false
        fi
        
        # Check if Nginx is listening on ports 80 and 443
        if netstat -tuln | grep -q ':80\s'; then
            log_success "Nginx is listening on port 80"
            verification_results["nginx_port_80"]="PASS"
        else
            log_error "Nginx is not listening on port 80"
            verification_results["nginx_port_80"]="FAIL"
            verification_passed=false
        fi
        
        if [[ "$SSL_MODE" != "none" ]]; then
            if netstat -tuln | grep -q ':443\s'; then
                log_success "Nginx is listening on port 443"
                verification_results["nginx_port_443"]="PASS"
            else
                log_error "Nginx is not listening on port 443"
                verification_results["nginx_port_443"]="FAIL"
                verification_passed=false
            fi
        fi
    else
        log_error "Nginx is not running"
        verification_results["nginx_status"]="FAIL"
        verification_passed=false
    fi
    
    # Check PostgreSQL status
    if service_is_active postgresql; then
        log_success "PostgreSQL is running"
        verification_results["postgresql_status"]="PASS"
        
        # Check PostgreSQL version
        pg_version=$(psql --version | awk '{print $3}')
        log_info "PostgreSQL version: $pg_version"
        
        # Check database connection
        if sudo -u hauling_app psql -U hauling_app -d hauling_qr_system -c "SELECT 1" > /dev/null 2>&1; then
            log_success "Database connection successful"
            verification_results["db_connection"]="PASS"
            
            # Check database tables
            table_count=$(sudo -u hauling_app psql -U hauling_app -d hauling_qr_system -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'" | xargs)
            if [[ $table_count -gt 0 ]]; then
                log_success "Database contains $table_count tables"
                verification_results["db_tables"]="PASS"
            else
                log_error "Database contains no tables"
                verification_results["db_tables"]="FAIL"
                verification_passed=false
            fi
        else
            log_error "Database connection failed"
            verification_results["db_connection"]="FAIL"
            verification_passed=false
        fi
    else
        log_error "PostgreSQL is not running"
        verification_results["postgresql_status"]="FAIL"
        verification_passed=false
    fi
    
    # Check Node.js application
    log_step "Verifying Node.js application"
    
    # Check PM2 processes
    if command -v pm2 &> /dev/null; then
        log_success "PM2 is installed"
        verification_results["pm2_installed"]="PASS"
        
        if sudo -u hauling_app pm2 list | grep -q "hauling-qr-api"; then
            log_success "Application process is running"
            verification_results["app_process"]="PASS"
            
            # Check process status
            process_status=$(sudo -u hauling_app pm2 show hauling-qr-api | grep status | awk '{print $4}')
            if [[ "$process_status" == "online" ]]; then
                log_success "Application process status: online"
                verification_results["app_status"]="PASS"
            else
                log_error "Application process status: $process_status"
                verification_results["app_status"]="FAIL"
                verification_passed=false
            fi
            
            # Check restart count
            restart_count=$(sudo -u hauling_app pm2 show hauling-qr-api | grep restart | awk '{print $3}')
            if [[ $restart_count -gt 3 ]]; then
                log_warning "Application has restarted $restart_count times. This may indicate instability."
                verification_results["app_restarts"]="WARNING"
                verification_warnings=$((verification_warnings + 1))
            else
                log_success "Application restart count: $restart_count"
                verification_results["app_restarts"]="PASS"
            fi
        else
            log_error "Application process is not running"
            verification_results["app_process"]="FAIL"
            verification_passed=false
        fi
    else
        log_error "PM2 is not installed"
        verification_results["pm2_installed"]="FAIL"
        verification_passed=false
    fi
    
    # Check API health endpoint
    if curl -s http://localhost:5000/api/health | grep -q "ok"; then
        log_success "API health check successful"
        verification_results["api_health"]="PASS"
        
        # Check API version endpoint
        api_version=$(curl -s http://localhost:5000/api/version 2>/dev/null || echo "N/A")
        log_info "API version: $api_version"
    else
        log_error "API health check failed"
        verification_results["api_health"]="FAIL"
        verification_passed=false
    fi
    
    # Check frontend files
    log_step "Verifying frontend files"
    
    if [[ -f "/var/www/hauling-qr-system/client/build/index.html" ]]; then
        log_success "Frontend build exists"
        verification_results["frontend_build"]="PASS"
        
        # Check key frontend files
        if [[ -f "/var/www/hauling-qr-system/client/build/static/js/main."*".js" ]]; then
            log_success "Frontend JavaScript bundle exists"
            verification_results["frontend_js"]="PASS"
        else
            log_error "Frontend JavaScript bundle is missing"
            verification_results["frontend_js"]="FAIL"
            verification_passed=false
        fi
        
        if [[ -f "/var/www/hauling-qr-system/client/build/static/css/main."*".css" ]]; then
            log_success "Frontend CSS bundle exists"
            verification_results["frontend_css"]="PASS"
        else
            log_error "Frontend CSS bundle is missing"
            verification_results["frontend_css"]="FAIL"
            verification_passed=false
        fi
    else
        log_error "Frontend build is missing"
        verification_results["frontend_build"]="FAIL"
        verification_passed=false
    fi
    
    # Check SSL configuration if enabled
    if [[ "$SSL_MODE" != "none" ]]; then
        log_step "Verifying SSL configuration"
        
        # Check SSL certificates
        if [[ -f "/etc/nginx/ssl/certificate.crt" && -f "/etc/nginx/ssl/private.key" ]]; then
            log_success "SSL certificates exist"
            verification_results["ssl_certs"]="PASS"
            
            # Check certificate validity
            cert_expiry=$(openssl x509 -enddate -noout -in /etc/nginx/ssl/certificate.crt | cut -d= -f2)
            log_info "SSL certificate expires on: $cert_expiry"
            
            # Check if certificate is valid for domain
            cert_domain=$(openssl x509 -subject -noout -in /etc/nginx/ssl/certificate.crt | grep -o "CN=[^,]*" | cut -d= -f2)
            if [[ "$cert_domain" == "$DOMAIN_NAME" || "$cert_domain" == "*.$DOMAIN_NAME" ]]; then
                log_success "SSL certificate is valid for domain: $DOMAIN_NAME"
                verification_results["ssl_domain"]="PASS"
            else
                log_warning "SSL certificate domain ($cert_domain) doesn't match deployment domain ($DOMAIN_NAME)"
                verification_results["ssl_domain"]="WARNING"
                verification_warnings=$((verification_warnings + 1))
            fi
        else
            log_error "SSL certificates are missing"
            verification_results["ssl_certs"]="FAIL"
            verification_passed=false
        fi
        
        # Check HTTPS connectivity
        if curl -s -k https://localhost/api/health > /dev/null; then
            log_success "Local HTTPS connectivity successful"
            verification_results["https_local"]="PASS"
        else
            log_warning "Local HTTPS check failed - may need manual verification"
            verification_results["https_local"]="WARNING"
            verification_warnings=$((verification_warnings + 1))
        fi
    fi
    
    # Check security configuration
    log_step "Verifying security configuration"
    
    # Check firewall status
    if ufw status | grep -q "Status: active"; then
        log_success "Firewall is active"
        verification_results["firewall_active"]="PASS"
        
        # Check required ports
        if ufw status | grep -q "80/tcp.*ALLOW"; then
            log_success "Firewall allows HTTP traffic"
            verification_results["firewall_http"]="PASS"
        else
            log_warning "Firewall may be blocking HTTP traffic"
            verification_results["firewall_http"]="WARNING"
            verification_warnings=$((verification_warnings + 1))
        fi
        
        if ufw status | grep -q "443/tcp.*ALLOW"; then
            log_success "Firewall allows HTTPS traffic"
            verification_results["firewall_https"]="PASS"
        else
            log_warning "Firewall may be blocking HTTPS traffic"
            verification_results["firewall_https"]="WARNING"
            verification_warnings=$((verification_warnings + 1))
        fi
    else
        log_warning "Firewall is not active"
        verification_results["firewall_active"]="WARNING"
        verification_warnings=$((verification_warnings + 1))
    fi
    
    # Check Fail2Ban status
    if systemctl is-active --quiet fail2ban; then
        log_success "Fail2Ban is running"
        verification_results["fail2ban"]="PASS"
    else
        log_warning "Fail2Ban is not running"
        verification_results["fail2ban"]="WARNING"
        verification_warnings=$((verification_warnings + 1))
    fi
    
    # Check file permissions
    if [[ -f "/var/www/hauling-qr-system/.env" ]]; then
        env_perms=$(stat -c "%a" /var/www/hauling-qr-system/.env)
        if [[ "$env_perms" == "640" || "$env_perms" == "600" ]]; then
            log_success "Environment file has secure permissions"
            verification_results["env_perms"]="PASS"
        else
            log_warning "Environment file has insecure permissions: $env_perms"
            verification_results["env_perms"]="WARNING"
            verification_warnings=$((verification_warnings + 1))
        fi
    fi
    
    # Check Cloudflare configuration if using Cloudflare SSL
    if [[ "$SSL_MODE" == "cloudflare" ]]; then
        log_step "Cloudflare SSL Configuration Instructions"
        log_info "Please complete the following steps in your Cloudflare dashboard:"
        log_info "1. Log in to Cloudflare and select the domain 'truckhaul.top'"
        log_info "2. Go to SSL/TLS → Overview and set encryption mode to 'Full'"
        log_info "3. Go to SSL/TLS → Edge Certificates and enable 'Always Use HTTPS'"
        log_info "4. Go to DNS and verify A records point to this server's IP address"
        log_info "5. Ensure the proxy status is enabled (orange cloud icon) for all records"
        log_info ""
        log_info "For detailed Cloudflare configuration instructions, please refer to:"
        log_info "CLOUDFLARE_SETUP.md (created in the current directory)"
        
        # Copy Cloudflare setup guide to the server
        cp CLOUDFLARE_SETUP.md /var/www/hauling-qr-system/
        chown hauling_app:hauling_app /var/www/hauling-qr-system/CLOUDFLARE_SETUP.md
    fi
    
    # Check monitoring and maintenance configuration
    log_step "Verifying monitoring and maintenance configuration"
    
    # Check health check script
    if [[ -f "/usr/local/bin/hauling-health-check.sh" ]]; then
        log_success "Health check script exists"
        verification_results["health_check_script"]="PASS"
        
        # Check if health check is in crontab
        if grep -q "hauling-health-check.sh" /etc/cron.d/hauling-health-check 2>/dev/null; then
            log_success "Health check is scheduled in crontab"
            verification_results["health_check_cron"]="PASS"
        else
            log_warning "Health check is not scheduled in crontab"
            verification_results["health_check_cron"]="WARNING"
            verification_warnings=$((verification_warnings + 1))
        fi
    else
        log_warning "Health check script is missing"
        verification_results["health_check_script"]="WARNING"
        verification_warnings=$((verification_warnings + 1))
    fi
    
    # Check backup configuration
    if [[ "$BACKUP_ENABLED" == true ]]; then
        if [[ -f "/usr/local/bin/backup-hauling-db.sh" ]]; then
            log_success "Database backup script exists"
            verification_results["backup_script"]="PASS"
            
            # Check if backup is in crontab
            if grep -q "backup-hauling-db.sh" /etc/cron.d/hauling-db-backup 2>/dev/null; then
                log_success "Database backup is scheduled in crontab"
                verification_results["backup_cron"]="PASS"
            else
                log_warning "Database backup is not scheduled in crontab"
                verification_results["backup_cron"]="WARNING"
                verification_warnings=$((verification_warnings + 1))
            fi
        else
            log_warning "Database backup script is missing"
            verification_results["backup_script"]="WARNING"
            verification_warnings=$((verification_warnings + 1))
        fi
    fi
    
    # Check log rotation
    if [[ -f "/etc/logrotate.d/hauling-qr-system" ]]; then
        log_success "Log rotation configuration exists"
        verification_results["log_rotation"]="PASS"
    else
        log_warning "Log rotation configuration is missing"
        verification_results["log_rotation"]="WARNING"
        verification_warnings=$((verification_warnings + 1))
    fi
    
    # Generate verification summary
    log_section "Verification Summary"
    
    # Count results
    pass_count=0
    fail_count=0
    warning_count=$verification_warnings
    
    for result in "${verification_results[@]}"; do
        if [[ "$result" == "PASS" ]]; then
            pass_count=$((pass_count + 1))
        elif [[ "$result" == "FAIL" ]]; then
            fail_count=$((fail_count + 1))
        fi
    done
    
    total_checks=$((pass_count + fail_count + warning_count))
    
    log_info "Total checks: $total_checks"
    log_success "Passed: $pass_count"
    log_warning "Warnings: $warning_count"
    log_error "Failed: $fail_count"
    
    # Create verification report file
    verification_report="/var/lib/hauling-deployment/verification-report.md"
    mkdir -p "$(dirname "$verification_report")"
    
    cat > "$verification_report" << EOF
# Hauling QR Trip Management System Verification Report

## Summary

- **Date:** $(date '+%Y-%m-%d %H:%M:%S')
- **Domain:** $DOMAIN_NAME
- **Environment:** $ENV_MODE
- **SSL Mode:** $SSL_MODE
- **Total Checks:** $total_checks
- **Passed:** $pass_count
- **Warnings:** $warning_count
- **Failed:** $fail_count
- **Overall Status:** $(if [[ "$verification_passed" == true ]]; then echo "✅ PASSED"; else echo "❌ FAILED"; fi)

## Detailed Results

$(for key in "${!verification_results[@]}"; do
    status="${verification_results[$key]}"
    status_icon="❓"
    if [[ "$status" == "PASS" ]]; then
        status_icon="✅"
    elif [[ "$status" == "FAIL" ]]; then
        status_icon="❌"
    elif [[ "$status" == "WARNING" ]]; then
        status_icon="⚠️"
    fi
    echo "- $status_icon $key: $status"
done)

## Next Steps

$(if [[ "$verification_passed" == true ]]; then
    if [[ $warning_count -gt 0 ]]; then
        echo "- The deployment was successful but has $warning_count warnings that should be addressed."
        echo "- Review the warnings above and take appropriate action."
    else
        echo "- The deployment was completely successful with no warnings or failures."
        echo "- The system is ready for production use."
    fi
else
    echo "- The deployment has $fail_count failed checks that must be resolved."
    echo "- Review the failures above and take appropriate action."
    echo "- Re-run the verification after addressing the issues."
fi)

## Verification Details

- **System:** $(lsb_release -d 2>/dev/null | cut -f2- || cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
- **CPU:** $(grep "model name" /proc/cpuinfo | head -n1 | cut -d':' -f2 | xargs)
- **Memory:** $(free -h | grep Mem | awk '{print $2}') total
- **Disk:** $(df -h / | awk 'NR==2 {print $2}') total, $(df -h / | awk 'NR==2 {print $4}') available
- **Verification Time:** $(date '+%Y-%m-%d %H:%M:%S')
EOF
    
    log_info "Verification report saved to $verification_report"
    
    if [[ "$verification_passed" == true ]]; then
        if [[ $warning_count -gt 0 ]]; then
            log_warning "Deployment verification completed with $warning_count warnings"
        else
            log_success "Deployment verification completed successfully"
        fi
    else
        log_error "Deployment verification failed with $fail_count errors"
    fi
    
    return $(if [[ "$verification_passed" == true ]]; then echo 0; else echo 1; fi)
}

generate_report() {
    log_section "Deployment Report"
    
    # Generate summary
    cat << EOF

=== Hauling QR Trip Management System Deployment Summary ===

Domain: $DOMAIN_NAME
Environment: $ENV_MODE
SSL Mode: $SSL_MODE
Database: hauling_qr_system
Admin User: $ADMIN_USERNAME

Access URLs:
- Frontend: https://$DOMAIN_NAME
- API: https://$DOMAIN_NAME/api
- WebSocket: wss://$DOMAIN_NAME/ws

Monitoring:
- Health Checks: $([ "$MONITORING_ENABLED" == true ] && echo "Enabled (every 5 minutes)" || echo "Disabled")
- Database Backups: $([ "$BACKUP_ENABLED" == true ] && echo "Enabled (daily at 2 AM, retained for $BACKUP_RETENTION_DAYS days)" || echo "Disabled")

Security:
- Firewall: Enabled (SSH, HTTP, HTTPS, API port)
- Fail2Ban: Enabled (SSH protection)
- SSL: $([[ "$SSL_MODE" != "none" ]] && echo "Enabled ($SSL_MODE)" || echo "Disabled")

Next Steps:
1. Complete Cloudflare configuration:
   - Log in to Cloudflare dashboard
   - Set SSL/TLS encryption mode to "Full"
   - Enable "Always Use HTTPS" option
   - Verify DNS records point to this server's IP address
   - Ensure proxy status is enabled (orange cloud icon)
   - For detailed instructions, see: /var/www/hauling-qr-system/CLOUDFLARE_SETUP.md

2. Access the system at https://$DOMAIN_NAME

3. Log in with the following credentials:
   - Username: $ADMIN_USERNAME
   - Email: $ADMIN_EMAIL
   - Password: $([ -z "$ADMIN_PASSWORD" ] && echo "[auto-generated password shown above]" || echo "[your provided password]")

Documentation:
- Cloudflare Setup: /var/www/hauling-qr-system/CLOUDFLARE_SETUP.md
- Deployment Log: $LOG_FILE
2. Log in with the admin credentials
3. Complete the initial system setup
4. Set up additional users as needed

For troubleshooting, check:
- Application logs: /var/www/hauling-qr-system/server/logs/
- Nginx logs: /var/log/nginx/
- PM2 logs: sudo -u hauling_app pm2 logs

EOF
    
    # Save report to file
    cat > /var/www/hauling-qr-system/deployment-report.txt << EOF
Hauling QR Trip Management System Deployment Report
Date: $(date)
Domain: $DOMAIN_NAME
Environment: $ENV_MODE
SSL Mode: $SSL_MODE
Database: hauling_qr_system
Admin User: $ADMIN_USERNAME
EOF
    
    log_success "Deployment report generated"
}

# ==============================
# Command-line Argument Parsing
# ==============================

parse_arguments() {
    log_step "Parsing command-line arguments"
    current_step="parse_arguments"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -c|--config)
                CONFIG_FILE="$2"
                log_debug "Using configuration file: $CONFIG_FILE"
                shift 2
                ;;
            -d|--domain)
                DOMAIN_NAME="$2"
                log_debug "Setting domain name: $DOMAIN_NAME"
                shift 2
                ;;
            -e|--env)
                ENV_MODE="$2"
                log_debug "Setting environment mode: $ENV_MODE"
                shift 2
                ;;
            -n|--non-interactive)
                INTERACTIVE=false
                log_debug "Running in non-interactive mode"
                shift
                ;;
            -q|--quiet)
                VERBOSE=false
                CONSOLE_OUTPUT=false
                log_debug "Running in quiet mode"
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                log_debug "Running in dry-run mode"
                shift
                ;;
            --log-level)
                case "$2" in
                    debug)
                        CURRENT_LOG_LEVEL=$LOG_LEVEL_DEBUG
                        ;;
                    info)
                        CURRENT_LOG_LEVEL=$LOG_LEVEL_INFO
                        ;;
                    warning)
                        CURRENT_LOG_LEVEL=$LOG_LEVEL_WARNING
                        ;;
                    error)
                        CURRENT_LOG_LEVEL=$LOG_LEVEL_ERROR
                        ;;
                    *)
                        echo "Invalid log level: $2. Using default (info)."
                        CURRENT_LOG_LEVEL=$LOG_LEVEL_INFO
                        ;;
                esac
                log_debug "Setting log level: $2"
                shift 2
                ;;
            --json-output)
                # Set up JSON-only output
                CONSOLE_OUTPUT=false
                LOG_JSON_ONLY=true
                log_debug "Using JSON output format"
                shift
                ;;
            --cicd-mode)
                enable_cicd_mode
                log_debug "CI/CD mode enabled"
                shift
                ;;
            --output-format)
                set_output_format "$2"
                shift 2
                ;;
            --output-file)
                STRUCTURED_OUTPUT_FILE="$2"
                log_debug "Structured output file: $STRUCTURED_OUTPUT_FILE"
                shift 2
                ;;
            --progress-indicators)
                case "$2" in
                    "true"|"yes"|"on")
                        PROGRESS_INDICATORS=true
                        ;;
                    "false"|"no"|"off")
                        PROGRESS_INDICATORS=false
                        ;;
                    *)
                        echo "Invalid value for --progress-indicators: $2. Use true/false."
                        exit $EXIT_CONFIG_ERROR
                        ;;
                esac
                log_debug "Progress indicators: $PROGRESS_INDICATORS"
                shift 2
                ;;
            --exit-codes)
                # Show available exit codes and their meanings
                show_exit_codes
                exit $EXIT_SUCCESS
                ;;
            --ssl-mode)
                SSL_MODE="$2"
                log_debug "Setting SSL mode: $SSL_MODE"
                shift 2
                ;;
            --admin-email)
                ADMIN_EMAIL="$2"
                log_debug "Setting admin email: $ADMIN_EMAIL"
                shift 2
                ;;
            --admin-user)
                ADMIN_USERNAME="$2"
                log_debug "Setting admin username: $ADMIN_USERNAME"
                shift 2
                ;;
            --repo-url)
                REPO_URL="$2"
                log_debug "Setting repository URL: $REPO_URL"
                shift 2
                ;;
            --repo-branch)
                REPO_BRANCH="$2"
                log_debug "Setting repository branch: $REPO_BRANCH"
                shift 2
                ;;
            --monitoring)
                MONITORING_ENABLED="$2"
                log_debug "Setting monitoring enabled: $MONITORING_ENABLED"
                shift 2
                ;;
            --backups)
                BACKUP_ENABLED="$2"
                log_debug "Setting backups enabled: $BACKUP_ENABLED"
                shift 2
                ;;
            --list-backups)
                list_available_backups
                exit 0
                ;;
            --show-backup)
                show_backup_details "$2"
                exit 0
                ;;
            --backup-retention)
                BACKUP_RETENTION_DAYS="$2"
                log_debug "Setting backup retention days: $BACKUP_RETENTION_DAYS"
                shift 2
                ;;
            --cleanup-backups)
                cleanup_old_backups
                exit 0
                ;;
            --create-backup)
                create_configuration_backup
                exit $?
                ;;
            --rollback)
                # Handle rollback with optional backup ID
                if [[ -n "$2" && "$2" != -* ]]; then
                    # Backup ID provided
                    rollback_deployment "$2" "false"
                    exit $?
                else
                    # No backup ID, use latest
                    rollback_deployment "" "false"
                    exit $?
                fi
                ;;
            --force-rollback)
                # Force rollback without confirmation
                if [[ -n "$2" && "$2" != -* ]]; then
                    # Backup ID provided
                    rollback_deployment "$2" "true"
                    exit $?
                else
                    # No backup ID, use latest
                    rollback_deployment "" "true"
                    exit $?
                fi
                ;;
            --output-format)
                OUTPUT_FORMAT="$2"
                log_debug "Setting output format: $OUTPUT_FORMAT"
                shift 2
                ;;
            --config-format)
                CONFIG_FORMAT="$2"
                log_debug "Setting config format: $CONFIG_FORMAT"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # If non-interactive mode is requested but no config file is provided
    if [[ "$INTERACTIVE" == false && -z "$CONFIG_FILE" && -z "$DOMAIN_NAME" ]]; then
        log_error "Non-interactive mode requires either a config file or domain name"
        show_help
        exit 1
    fi
    
    log_success "Command-line arguments parsed successfully"
}

show_help() {
    cat << EOF
Hauling QR Trip Management System Deployment Script for Ubuntu 24.04
Version: $VERSION

Usage: ./deploy-hauling-qr-ubuntu.sh [options]

Basic Options:
  -c, --config FILE       Path to configuration file (supports shell, JSON, and YAML formats)
  -d, --domain DOMAIN     Domain name for the application
  -e, --env MODE          Environment mode (production, staging, development)
  -n, --non-interactive   Run in non-interactive mode (requires config file or domain)
  -q, --quiet             Minimal output
  --dry-run               Validate configuration without making changes
  -h, --help              Show this help message

Advanced Options:
  --ssl-mode MODE         SSL mode (none, letsencrypt, cloudflare, custom)
  --admin-email EMAIL     Admin user email address
  --admin-user USERNAME   Admin username

Backup Management Options:
  --create-backup         Create a configuration backup and exit
  --list-backups          List all available configuration backups
  --show-backup ID        Show detailed information about a specific backup
  --cleanup-backups       Remove old backups based on retention policy
  --backup-retention DAYS Number of days to retain backups (default: 7)
  --backups true/false    Enable or disable automatic backups during deployment

Rollback Options:
  --rollback [BACKUP_ID]  Rollback to a specific backup (or latest if no ID provided)
  --force-rollback [ID]   Force rollback without confirmation prompts
  --repo-url URL          Git repository URL
  --repo-branch BRANCH    Git repository branch
  --monitoring BOOL       Enable monitoring (true/false)
  --backups BOOL          Enable backups (true/false)

Output Options:
  --log-level LEVEL       Set log level (debug, info, warning, error)
  --json-output           Output logs in JSON format for machine parsing
  --output-format FORMAT  Output format for reports (text, json, yaml)
  --config-format FORMAT  Force specific config file format (shell, json, yaml)

CI/CD Options:
  --cicd-mode             Enable CI/CD friendly mode with structured output
  --output-file FILE      Save structured output to specified file
  --progress-indicators BOOL  Enable/disable progress indicators (true/false)
  --exit-codes            Show available exit codes and their meanings

Examples:
  # Basic usage with interactive prompts
  ./deploy-hauling-qr-ubuntu.sh

  # Non-interactive deployment with configuration file
  ./deploy-hauling-qr-ubuntu.sh --config deployment-config.json --non-interactive

  # Non-interactive deployment with command-line parameters
  ./deploy-hauling-qr-ubuntu.sh --domain example.com --env production --non-interactive

  # Validate configuration without making changes
  ./deploy-hauling-qr-ubuntu.sh --config deployment-config.yaml --dry-run

  # CI/CD friendly output
  ./deploy-hauling-qr-ubuntu.sh --config deployment-config.json --non-interactive --json-output
  
  # Full CI/CD mode with structured output
  ./deploy-hauling-qr-ubuntu.sh --config config.yaml --cicd-mode --output-format json --output-file deployment.json
  
  # Quiet CI/CD deployment
  ./deploy-hauling-qr-ubuntu.sh --config config.yaml --cicd-mode --quiet --progress-indicators false

Backup Management Examples:
  # Create a configuration backup before manual changes
  ./deploy-hauling-qr-ubuntu.sh --create-backup
  
  # List all available backups
  ./deploy-hauling-qr-ubuntu.sh --list-backups
  
  # Show details of a specific backup
  ./deploy-hauling-qr-ubuntu.sh --show-backup 20250119_143022
  
  # Clean up old backups
  ./deploy-hauling-qr-ubuntu.sh --cleanup-backups
  
  # Deploy with custom backup retention
  ./deploy-hauling-qr-ubuntu.sh --config config.yaml --backup-retention 14

Rollback Examples:
  # Rollback to the latest backup (with confirmation)
  ./deploy-hauling-qr-ubuntu.sh --rollback
  
  # Rollback to a specific backup
  ./deploy-hauling-qr-ubuntu.sh --rollback 20250119_143022
  
  # Force rollback without confirmation (for automation)
  ./deploy-hauling-qr-ubuntu.sh --force-rollback
  
  # Force rollback to specific backup without confirmation
  ./deploy-hauling-qr-ubuntu.sh --force-rollback 20250119_143022
EOF
}

# ==============================
# Main Function
# ==============================

main() {
    # Initialize log file and directory
    mkdir -p $(dirname "$LOG_FILE")
    echo "=== Hauling QR Trip Management System Deployment Log ===" > "$LOG_FILE"
    echo "Date: $(date)" >> "$LOG_FILE"
    echo "Version: $VERSION" >> "$LOG_FILE"

    # Validate prerequisites first
    if ! validate_prerequisites; then
        log_error "Prerequisites validation failed. Deployment cannot continue."
        exit 1
    fi

    # Check and fix common system issues
    check_and_fix_system_issues

    # Check and install required dependencies with retry
    log_step "Checking for required dependencies"

    # Install jq for JSON processing
    if ! command -v jq &> /dev/null; then
        log_info "Installing jq for JSON processing..."
        if ! retry_with_backoff "apt-get update -y && apt-get install -y jq" 3 2 10 "jq installation"; then
            log_error "Failed to install jq after multiple attempts"
            exit 1
        fi

        # Verify installation
        if ! command -v jq &> /dev/null; then
            log_error "jq installation verification failed"
            exit 1
        fi
        log_success "jq installed successfully"
    else
        log_info "jq is already installed"
    fi

    # Install bc for calculations
    if ! command -v bc &> /dev/null; then
        log_info "Installing bc for calculations..."
        if ! retry_with_backoff "apt-get update -y && apt-get install -y bc" 3 2 10 "bc installation"; then
            log_error "Failed to install bc after multiple attempts"
            exit 1
        fi

        # Verify installation
        if ! command -v bc &> /dev/null; then
            log_error "bc installation verification failed"
            exit 1
        fi
        log_success "bc installed successfully"
    else
        log_info "bc is already installed"
    fi
    
    # Install openssl for password generation
    if ! command -v openssl &> /dev/null; then
        log_info "Installing openssl for password generation..."
        apt-get update -y
        apt-get install -y openssl
        
        # Verify installation
        if ! command -v openssl &> /dev/null; then
            log_error "Failed to install openssl. Please install it manually with: apt-get install -y openssl"
            exit 1
        fi
        log_success "openssl installed successfully"
    else
        log_info "openssl is already installed"
    fi
    
    # Log script start with version information
    log_section "Deployment Script Initialization"
    log_info "Hauling QR Trip Management System Deployment Script v$VERSION"
    log_info "Running on $(lsb_release -d 2>/dev/null | cut -f2- || cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
    log_info "Script started at $(date '+%Y-%m-%d %H:%M:%S')"
    
    # Log system information
    log_debug "System information: $(uname -a)"
    log_debug "CPU: $(grep "model name" /proc/cpuinfo | head -n1 | cut -d':' -f2 | xargs)"
    log_debug "Memory: $(free -h | grep Mem | awk '{print $2}') total, $(free -h | grep Mem | awk '{print $4}') available"
    log_debug "Disk: $(df -h / | awk 'NR==2 {print $2}') total, $(df -h / | awk 'NR==2 {print $4}') available"
    
    # Parse command-line arguments
    parse_arguments "$@"
    
    # Load configuration
    if [[ -n "$CONFIG_FILE" ]]; then
        source_config_file "$CONFIG_FILE"
        log_info "Configuration loaded from file: $CONFIG_FILE"
    elif [[ "$INTERACTIVE" == true ]]; then
        log_info "Starting interactive configuration"
        interactive_configuration
        log_info "Interactive configuration completed"
    fi
    
    # Validate configuration
    validate_configuration
    log_success "Configuration validated successfully"
    
    # Log configuration details (with sensitive information masked)
    log_debug "Domain: $DOMAIN_NAME"
    log_debug "Environment: $ENV_MODE"
    log_debug "SSL Mode: $SSL_MODE"
    log_debug "Admin User: $ADMIN_USERNAME"
    log_debug "Admin Email: $ADMIN_EMAIL"
    log_debug "DB Password: [MASKED]"
    log_debug "JWT Secret: [MASKED]"
    log_debug "Monitoring Enabled: $MONITORING_ENABLED"
    log_debug "Backup Enabled: $BACKUP_ENABLED"
    
    # Exit if dry run
    if [[ "$DRY_RUN" == true ]]; then
        log_section "Dry Run Completed"
        log_info "Configuration validated. Exiting due to dry-run mode."
        exit 0
    fi
    
    # Check if running as root
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
    
    # Initialize CI/CD output if enabled
    initialize_cicd_output
    
    # Start deployment
    log_section "Starting Deployment"
    log_info "Deployment started at $(date '+%Y-%m-%d %H:%M:%S')"
    log_info "Target domain: $DOMAIN_NAME"
    log_info "Environment: $ENV_MODE"
    
    # Output deployment progress for CI/CD
    output_deployment_progress "deployment_start" "started" "Starting deployment process" 0
    
    # Log initial system metrics
    log_system_metrics
    
    # Create configuration backup before making any changes
    show_progress "Creating configuration backup" 1 9
    output_deployment_progress "configuration_backup" "started" "Creating backup of existing configurations" 6
    if create_configuration_backup; then
        log_success "Configuration backup completed successfully"
        output_deployment_progress "configuration_backup" "completed" "Configuration backup completed successfully" 6
    else
        log_warning "Configuration backup completed with some errors, but deployment will continue"
        output_deployment_progress "configuration_backup" "completed_with_warnings" "Configuration backup completed with warnings" 6
    fi
    
    # Execute deployment phases with progress tracking
    show_progress "Preparing system" 2 9
    output_deployment_progress "system_preparation" "started" "Preparing system and installing dependencies" 17
    prepare_system
    log_success "System preparation completed"
    log_system_metrics
    output_deployment_progress "system_preparation" "completed" "System preparation completed successfully" 17
    
    show_progress "Setting up security" 3 9
    output_deployment_progress "security_setup" "started" "Configuring firewall and security settings" 28
    setup_security
    log_success "Security setup completed"
    output_deployment_progress "security_setup" "completed" "Security setup completed successfully" 28
    
    show_progress "Deploying application" 4 9
    output_deployment_progress "application_deployment" "started" "Cloning repository and building application" 39
    deploy_application
    log_success "Application deployment completed"
    log_system_metrics
    output_deployment_progress "application_deployment" "completed" "Application deployment completed successfully" 39
    
    show_progress "Setting up database" 5 9
    output_deployment_progress "database_setup" "started" "Configuring PostgreSQL database" 50
    setup_database
    log_success "Database setup completed"
    output_deployment_progress "database_setup" "completed" "Database setup completed successfully" 50
    
    show_progress "Configuring infrastructure" 6 9
    output_deployment_progress "infrastructure_config" "started" "Configuring Nginx and SSL" 61
    configure_infrastructure
    log_success "Infrastructure configuration completed"
    log_system_metrics
    output_deployment_progress "infrastructure_config" "completed" "Infrastructure configuration completed successfully" 61
    
    if [[ "$MONITORING_ENABLED" == true ]]; then
        show_progress "Setting up monitoring" 7 9
        output_deployment_progress "monitoring_setup" "started" "Setting up health checks and monitoring" 72
        setup_monitoring
        log_success "Monitoring setup completed"
        output_deployment_progress "monitoring_setup" "completed" "Monitoring setup completed successfully" 72
    else
        log_info "Monitoring setup skipped (disabled in configuration)"
        output_deployment_progress "monitoring_setup" "skipped" "Monitoring setup skipped (disabled)" 72
    fi
    
    show_progress "Verifying deployment" 8 9
    output_deployment_progress "deployment_verification" "started" "Verifying deployment and running health checks" 83
    verify_deployment
    log_success "Deployment verification completed"
    output_deployment_progress "deployment_verification" "completed" "Deployment verification completed successfully" 83
    
    # Generate deployment summary and report
    show_progress "Generating summary" 9 9
    output_deployment_progress "summary_generation" "started" "Generating deployment summary and reports" 100
    summary_file=$(generate_deployment_summary)
    
    # Record end time and log metrics
    DEPLOYMENT_END_TIME=$(date +%s)
    log_deployment_metrics "$DEPLOYMENT_START_TIME" "$DEPLOYMENT_END_TIME"
    
    # Output deployment metrics for CI/CD
    output_deployment_metrics "$DEPLOYMENT_START_TIME" "$DEPLOYMENT_END_TIME" "true"
    
    log_section "Deployment Completed Successfully"
    log_success "Deployment completed at $(date '+%Y-%m-%d %H:%M:%S')"
    
    # Output final deployment result for CI/CD
    local duration=$((DEPLOYMENT_END_TIME - DEPLOYMENT_START_TIME))
    output_deployment_result "true" "$EXIT_SUCCESS" "Deployment completed successfully" "$duration"
    
    # Display access information (only in non-CI/CD mode)
    if [[ "$CI_CD_MODE" != true && "$QUIET_MODE" != true ]]; then
        echo
        echo "Hauling QR Trip Management System has been deployed successfully!"
        echo
        echo "Access Information:"
        echo "- Frontend: https://$DOMAIN_NAME"
        echo "- Admin Dashboard: https://$DOMAIN_NAME/admin"
        echo "- Admin Username: $ADMIN_USERNAME"
        echo "- Admin Password: $ADMIN_PASSWORD"
        echo
        echo "Deployment log saved to: $LOG_FILE"
        echo "Deployment summary saved to: $summary_file"
        echo
    fi
    
    # Finalize CI/CD output
    finalize_cicd_output
    
    # Final log entry
    log_info "Deployment script execution completed successfully"
    
    # Exit with success code
    exit $EXIT_SUCCESS
}

# Execute main function
main "$@"

# Function to generate a deployment summary report
generate_deployment_summary() {
    log_section "Generating Deployment Summary"
    
    # Create summary directory if it doesn't exist
    local summary_dir="/var/lib/hauling-deployment/summary"
    mkdir -p "$summary_dir"
    
    # Generate timestamp for the summary file
    local timestamp=$(date '+%Y%m%d-%H%M%S')
    local summary_file="$summary_dir/deployment-summary-$timestamp.md"
    
    # Create the summary file
    cat > "$summary_file" << EOF
# Hauling QR Trip Management System Deployment Summary

## Deployment Information

- **Date:** $(date '+%Y-%m-%d %H:%M:%S')
- **Domain:** $DOMAIN_NAME
- **Environment:** $ENV_MODE
- **SSL Mode:** $SSL_MODE
- **Deployment Script Version:** $VERSION

## System Information

- **Hostname:** $(hostname)
- **IP Address:** $(hostname -I | awk '{print $1}')
- **Operating System:** $(lsb_release -d 2>/dev/null | cut -f2- || cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
- **Kernel:** $(uname -r)
- **CPU:** $(grep "model name" /proc/cpuinfo | head -n1 | cut -d':' -f2 | xargs)
- **Memory:** $(free -h | grep Mem | awk '{print $2}') total
- **Disk:** $(df -h / | awk 'NR==2 {print $2}') total, $(df -h / | awk 'NR==2 {print $4}') available

## Component Status

### Installation Summary
$COMPONENT_DETECTION_SUMMARY

### Detailed Component Information

$(generate_component_details_for_summary)

## Installed Components

- **Web Server:** Nginx $(nginx -v 2>&1 | cut -d'/' -f2)
- **Database:** PostgreSQL $(psql --version | awk '{print $3}')
- **Node.js:** $(node -v)
- **NPM:** $(npm -v)
- **PM2:** $(pm2 -v 2>/dev/null || echo "Not installed")

## Configuration Summary

- **Backend API URL:** https://$DOMAIN_NAME/api
- **WebSocket URL:** wss://$DOMAIN_NAME/ws
- **Database Name:** hauling_qr_system
- **Admin Username:** $ADMIN_USERNAME
- **Admin Email:** $ADMIN_EMAIL

## Access Information

- **Frontend URL:** https://$DOMAIN_NAME
- **Admin Dashboard:** https://$DOMAIN_NAME/admin
- **API Documentation:** https://$DOMAIN_NAME/api/docs

## Monitoring & Maintenance

- **Health Check URL:** https://$DOMAIN_NAME/api/health
- **Health Check Script:** /usr/local/bin/hauling-health-check.sh
- **Database Backup Directory:** /var/backups/hauling-qr-system
- **Log Files:**
  - Application Logs: /var/www/hauling-qr-system/server/logs/
  - Nginx Access Logs: /var/log/nginx/access.log
  - Nginx Error Logs: /var/log/nginx/error.log
  - Deployment Logs: $LOG_FILE

## Next Steps

1. **Verify Installation:**
   - Access the frontend at https://$DOMAIN_NAME
   - Log in with the admin credentials
   - Check the health endpoint at https://$DOMAIN_NAME/api/health

2. **Cloudflare Configuration:**
   - Ensure your Cloudflare DNS settings are correctly configured
   - Set SSL/TLS encryption mode to "Full"
   - Enable "Always Use HTTPS" option

3. **Security Recommendations:**
   - Change the default admin password
   - Set up regular security updates
   - Review firewall rules periodically

4. **Backup Recommendations:**
   - Verify backup configuration
   - Test backup restoration process
   - Set up off-site backup storage

## Troubleshooting

If you encounter issues with the deployment:

1. Check the deployment logs at $LOG_FILE
2. Review the error report at /tmp/hauling-deployment-error.log
3. Check the application logs at /var/www/hauling-qr-system/server/logs/
4. Verify Nginx configuration with \`nginx -t\`
5. Check database connectivity with \`sudo -u hauling_app psql -U hauling_app -d hauling_qr_system -c "SELECT 1"\`

For additional assistance, please refer to the documentation or contact the system administrator.
EOF
    
    log_success "Deployment summary generated at $summary_file"
    
    # Create a symlink to the latest summary
    ln -sf "$summary_file" "$summary_dir/latest-summary.md"
    
    # Return the path to the summary file
    echo "$summary_file"
}

# Function to log deployment metrics
log_deployment_metrics() {
    local start_time="$1"
    local end_time="$2"
    local duration=$((end_time - start_time))
    
    # Format duration as hours:minutes:seconds
    local hours=$((duration / 3600))
    local minutes=$(( (duration % 3600) / 60 ))
    local seconds=$((duration % 60))
    local formatted_duration=$(printf "%02d:%02d:%02d" $hours $minutes $seconds)
    
    log_info "Deployment completed in $formatted_duration"
    
    # Log detailed metrics
    cat >> "$LOG_FILE" << EOF

#---------- DEPLOYMENT METRICS ----------#
Start Time: $(date -d @$start_time '+%Y-%m-%d %H:%M:%S')
End Time: $(date -d @$end_time '+%Y-%m-%d %H:%M:%S')
Duration: $formatted_duration
Domain: $DOMAIN_NAME
Environment: $ENV_MODE
SSL Mode: $SSL_MODE
Interactive Mode: $INTERACTIVE
System: $(lsb_release -d 2>/dev/null | cut -f2- || cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
CPU Usage: $(grep 'cpu ' /proc/stat | awk '{usage=($2+$4)*100/($2+$4+$5)} END {print usage "%"}')
Memory Usage: $(free | grep Mem | awk '{print $3/$2 * 100.0 "%"}')
Disk Usage: $(df -h / | awk 'NR==2 {print $5}')
EOF
    
    # Create JSON metrics file
    local metrics_file="/var/lib/hauling-deployment/metrics.json"
    mkdir -p "/var/lib/hauling-deployment"
    
    # Create or update metrics file
    if [[ ! -f "$metrics_file" ]]; then
        echo '{"deployments":[]}' > "$metrics_file"
    fi
    
    # Add new deployment metrics
    local metrics_json="{\"timestamp\":$(date +%s),\"date\":\"$(date '+%Y-%m-%d %H:%M:%S')\",\"domain\":\"$DOMAIN_NAME\",\"environment\":\"$ENV_MODE\",\"ssl_mode\":\"$SSL_MODE\",\"duration_seconds\":$duration,\"interactive\":$INTERACTIVE}"
    
    # Append to metrics file
    local temp_file=$(mktemp)
    jq ".deployments += [$metrics_json]" "$metrics_file" > "$temp_file" && mv "$temp_file" "$metrics_file"
}

# Function to generate a strong password with specified length
generate_strong_password() {
    local length=$1
    local password=""
    
    # Ensure we have the tools we need
    if command -v pwgen &> /dev/null; then
        # Use pwgen if available (more secure)
        password=$(pwgen -s -y -B $length 1)
    else
        # Fallback to OpenSSL with custom character set
        local chars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()-_=+[]{}|;:,.<>?"
        local chars_length=${#chars}
        
        # Generate random bytes and map to our character set
        for (( i=0; i<$length; i++ )); do
            # Get a random byte value
            local random_byte=$(openssl rand -hex 1)
            local decimal_value=$((16#$random_byte))
            
            # Map to our character set
            local index=$((decimal_value % chars_length))
            password="${password}${chars:$index:1}"
        done
        
        # Ensure password has at least one of each required character type
        ensure_password_complexity "$password" "$length"
    fi
    
    echo "$password"
}

# Function to ensure password has required complexity
ensure_password_complexity() {
    local password=$1
    local length=$2
    
    # Check if password has at least one uppercase, lowercase, digit, and special char
    if [[ ! "$password" =~ [A-Z] ]] || [[ ! "$password" =~ [a-z] ]] || [[ ! "$password" =~ [0-9] ]] || [[ ! "$password" =~ [[:punct:]] ]]; then
        # If not, generate a new password that meets requirements
        local new_password=""
        local special_chars="!@#$%^&*()-_=+[]{}|;:,.<>?"
        
        # Add one of each required character type
        new_password="${new_password}$(openssl rand -base64 1 | tr -dc 'A-Z' | head -c1)"
        new_password="${new_password}$(openssl rand -base64 1 | tr -dc 'a-z' | head -c1)"
        new_password="${new_password}$(openssl rand -base64 1 | tr -dc '0-9' | head -c1)"
        new_password="${new_password}${special_chars:$(( RANDOM % ${#special_chars} )):1}"
        
        # Fill the rest with random characters
        local remaining_length=$((length - 4))
        local chars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()-_=+[]{}|;:,.<>?"
        
        for (( i=0; i<$remaining_length; i++ )); do
            new_password="${new_password}${chars:$(( RANDOM % ${#chars} )):1}"
        done
        
        # Shuffle the password
        password=$(echo "$new_password" | fold -w1 | shuf | tr -d '\n')
    fi
    
    echo "$password"
}

# Function to validate password strength
validate_password_strength() {
    local password=$1
    local type=$2
    local min_length=${3:-16}  # Default minimum length is 16
    
    local strength="Weak"
    local has_uppercase=0
    local has_lowercase=0
    local has_digit=0
    local has_special=0
    
    # Check password length
    if [[ ${#password} -lt $min_length ]]; then
        log_warning "$type password is too short (${#password} chars). Minimum recommended length is $min_length chars."
    fi
    
    # Check for uppercase letters
    if [[ "$password" =~ [A-Z] ]]; then
        has_uppercase=1
    else
        log_warning "$type password does not contain uppercase letters."
    fi
    
    # Check for lowercase letters
    if [[ "$password" =~ [a-z] ]]; then
        has_lowercase=1
    else
        log_warning "$type password does not contain lowercase letters."
    fi
    
    # Check for digits
    if [[ "$password" =~ [0-9] ]]; then
        has_digit=1
    else
        log_warning "$type password does not contain digits."
    fi
    
    # Check for special characters
    if [[ "$password" =~ [[:punct:]] ]]; then
        has_special=1
    else
        log_warning "$type password does not contain special characters."
    fi
    
    # Calculate strength score
    local strength_score=$((has_uppercase + has_lowercase + has_digit + has_special))
    
    # Determine strength based on score and length
    if [[ $strength_score -eq 4 && ${#password} -ge $((min_length * 2)) ]]; then
        strength="Very Strong"
    elif [[ $strength_score -eq 4 && ${#password} -ge $min_length ]]; then
        strength="Strong"
    elif [[ $strength_score -ge 3 && ${#password} -ge $min_length ]]; then
        strength="Moderate"
    else
        strength="Weak"
        log_warning "$type password strength is $strength. Consider using a stronger password."
    fi
    
    log_debug "$type password strength: $strength"
}

# Function to generate configuration file templates
generate_config_template() {
    local format="$1"
    local output_file="$2"
    
    log_step "Generating configuration template in $format format"
    
    case "$format" in
        json)
            generate_json_config_template "$output_file"
            ;;
        yaml|yml)
            generate_yaml_config_template "$output_file"
            ;;
        shell|conf)
            generate_shell_config_template "$output_file"
            ;;
    esac
}

# Function to parse shell format configuration file
parse_shell_config() {
    local config_file=$1
    log_info "Parsing shell format configuration file"
    
    # Check for common syntax errors before sourcing
    if ! bash -n "$config_file" 2>/dev/null; then
        log_error "Shell syntax error in configuration file"
        log_error "Please check your configuration file for syntax errors"
        exit 1
    fi
    
    # Source the shell-format configuration file
    source "$config_file"
    
    # Check for required variables
    local missing_vars=()
    if [[ -z "$DOMAIN_NAME" ]]; then missing_vars+=("DOMAIN_NAME"); fi
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_warning "Some recommended variables are not set in the configuration file:"
        for var in "${missing_vars[@]}"; do
            log_warning "- $var"
        done
        log_info "Default values will be used for missing variables"
    fi
    
    log_success "Shell configuration file parsed successfully"
}

# Function to validate the loaded configuration
validate_loaded_configuration() {
    log_step "Validating loaded configuration"
    
    # Validate domain name
    if [[ -z "$DOMAIN_NAME" ]]; then
        log_warning "Domain name not specified in configuration file"
        if [[ "$INTERACTIVE" == true ]]; then
            log_info "You will be prompted to enter a domain name"
        else
            log_error "Domain name is required in non-interactive mode"
            exit 1
        fi
    else
        # Validate domain name format
        if ! [[ "$DOMAIN_NAME" =~ ^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$ ]]; then
            log_error "Invalid domain name format: $DOMAIN_NAME"
            log_error "Domain name must be a valid hostname (e.g., example.com)"
            exit 1
        fi
    fi
    
    # Validate SSL mode
    if [[ -n "$SSL_MODE" ]]; then
        if [[ "$SSL_MODE" != "none" && "$SSL_MODE" != "letsencrypt" && "$SSL_MODE" != "cloudflare" && "$SSL_MODE" != "custom" ]]; then
            log_error "Invalid SSL mode: $SSL_MODE"
            log_error "Valid options: none, letsencrypt, cloudflare, custom"
            exit 1
        fi
    fi
    
    # Validate environment mode
    if [[ -n "$ENV_MODE" ]]; then
        if [[ "$ENV_MODE" != "production" && "$ENV_MODE" != "staging" && "$ENV_MODE" != "development" ]]; then
            log_error "Invalid environment mode: $ENV_MODE"
            log_error "Valid options: production, staging, development"
            exit 1
        fi
    fi
    
    # Validate boolean values
    for var_name in MONITORING_ENABLED BACKUP_ENABLED; do
        if [[ -n "${!var_name}" ]]; then
            if [[ "${!var_name}" != "true" && "${!var_name}" != "false" ]]; then
                log_error "Invalid value for $var_name: ${!var_name}"
                log_error "Valid options: true, false"
                exit 1
            fi
        fi
    done
    
    # Validate email format if provided
    if [[ -n "$ADMIN_EMAIL" ]]; then
        if ! [[ "$ADMIN_EMAIL" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
            log_error "Invalid email format: $ADMIN_EMAIL"
            exit 1
        fi
    fi
    
    # Validate password strength if provided
    if [[ -n "$ADMIN_PASSWORD" ]]; then
        validate_password_strength "$ADMIN_PASSWORD" "admin"
    fi
    
    if [[ -n "$DB_PASSWORD" ]]; then
        validate_password_strength "$DB_PASSWORD" "database"
    fi
    
    if [[ -n "$JWT_SECRET" ]]; then
        validate_password_strength "$JWT_SECRET" "JWT secret" 48
    fi
    
    log_success "Configuration validation completed"
}

# Function to install yq YAML processor
install_yq_processor() {
    log_info "Installing yq YAML processor..."
    
    # Detect architecture
    local arch=$(uname -m)
    local yq_arch=""
    
    case $arch in
        x86_64)
            yq_arch="amd64"
            ;;
        aarch64|arm64)
            yq_arch="arm64"
            ;;
        armv7l)
            yq_arch="arm"
            ;;
        *)
            log_error "Unsupported architecture: $arch"
            exit 1
            ;;
    esac
    
    # Download and install yq
    local yq_version="v4.35.2"  # Use a specific version for consistency
    local yq_url="https://github.com/mikefarah/yq/releases/download/${yq_version}/yq_linux_${yq_arch}"
    
    log_debug "Downloading yq from: $yq_url"
    
    if command -v wget &> /dev/null; then
        wget -q -O /usr/local/bin/yq "$yq_url"
    elif command -v curl &> /dev/null; then
        curl -s -L -o /usr/local/bin/yq "$yq_url"
    else
        log_error "Neither wget nor curl is available. Cannot download yq."
        exit 1
    fi
    
    # Make executable and verify installation
    chmod +x /usr/local/bin/yq
    
    if ! /usr/local/bin/yq --version &> /dev/null; then
        log_error "Failed to install yq YAML processor"
        exit 1
    fi
    
    log_success "yq YAML processor installed successfully"
}

# Function to validate YAML configuration structure
validate_yaml_structure() {
    local config_file=$1
    log_debug "Validating YAML configuration structure"
    
    # Check for required top-level fields
    local required_fields=("domainName")
    local missing_fields=()
    
    for field in "${required_fields[@]}"; do
        if [[ $(yq eval "has(\"$field\")" "$config_file") != "true" ]]; then
            missing_fields+=("$field")
        fi
    done
    
    if [[ ${#missing_fields[@]} -gt 0 ]]; then
        log_error "Missing required configuration fields: ${missing_fields[*]}"
        log_error "Please ensure your YAML configuration includes all required fields"
        exit 1
    fi
    
    # Validate field types and values
    validate_yaml_field_types "$config_file"
    
    # Validate field values
    validate_yaml_field_values "$config_file"
    
    log_success "YAML configuration structure validation passed"
}

# Function to validate YAML field types
validate_yaml_field_types() {
    local config_file=$1
    log_debug "Validating YAML field types"
    
    # Define expected types for fields
    declare -A field_types=(
        ["domainName"]="string"
        ["sslMode"]="string"
        ["environment"]="string"
        ["monitoringEnabled"]="boolean"
        ["backupEnabled"]="boolean"
        ["backupRetentionDays"]="number"
    )
    
    # Validate each field type
    for field in "${!field_types[@]}"; do
        if [[ $(yq eval "has(\"$field\")" "$config_file") == "true" ]]; then
            local expected_type="${field_types[$field]}"
            local actual_value=$(yq eval ".$field" "$config_file")
            
            case $expected_type in
                "string")
                    if [[ $(yq eval ".$field | type" "$config_file") != "!!str" ]]; then
                        log_warning "Field '$field' should be a string, got: $actual_value"
                    fi
                    ;;
                "boolean")
                    if [[ $(yq eval ".$field | type" "$config_file") != "!!bool" ]]; then
                        # Try to convert common string representations to boolean
                        case $(echo "$actual_value" | tr '[:upper:]' '[:lower:]') in
                            "true"|"yes"|"1"|"on"|"enabled")
                                log_debug "Converting '$actual_value' to boolean true for field '$field'"
                                ;;
                            "false"|"no"|"0"|"off"|"disabled")
                                log_debug "Converting '$actual_value' to boolean false for field '$field'"
                                ;;
                            *)
                                log_warning "Field '$field' should be a boolean, got: $actual_value"
                                ;;
                        esac
                    fi
                    ;;
                "number")
                    if [[ $(yq eval ".$field | type" "$config_file") != "!!int" && $(yq eval ".$field | type" "$config_file") != "!!float" ]]; then
                        if [[ "$actual_value" =~ ^[0-9]+$ ]]; then
                            log_debug "Field '$field' appears to be a numeric string: $actual_value"
                        else
                            log_warning "Field '$field' should be a number, got: $actual_value"
                        fi
                    fi
                    ;;
            esac
        fi
    done
}

# Function to validate YAML field values
validate_yaml_field_values() {
    local config_file=$1
    log_debug "Validating YAML field values"
    
    # Validate domain name format
    if [[ $(yq eval 'has("domainName")' "$config_file") == "true" ]]; then
        local domain_name=$(yq eval '.domainName' "$config_file")
        if [[ -n "$domain_name" ]]; then
            if ! validate_domain_name "$domain_name"; then
                log_error "Invalid domain name format: $domain_name"
                exit 1
            fi
        fi
    fi
    
    # Validate SSL mode
    if [[ $(yq eval 'has("sslMode")' "$config_file") == "true" ]]; then
        local ssl_mode=$(yq eval '.sslMode' "$config_file")
        if [[ -n "$ssl_mode" ]]; then
            case "$ssl_mode" in
                "none"|"letsencrypt"|"cloudflare"|"custom")
                    log_debug "Valid SSL mode: $ssl_mode"
                    ;;
                *)
                    log_error "Invalid SSL mode: $ssl_mode. Valid options: none, letsencrypt, cloudflare, custom"
                    exit 1
                    ;;
            esac
        fi
    fi
    
    # Validate environment
    if [[ $(yq eval 'has("environment")' "$config_file") == "true" ]]; then
        local environment=$(yq eval '.environment' "$config_file")
        if [[ -n "$environment" ]]; then
            case "$environment" in
                "production"|"staging"|"development")
                    log_debug "Valid environment: $environment"
                    ;;
                *)
                    log_error "Invalid environment: $environment. Valid options: production, staging, development"
                    exit 1
                    ;;
            esac
        fi
    fi
    
    # Validate backup retention days
    if [[ $(yq eval 'has("backupRetentionDays")' "$config_file") == "true" ]]; then
        local retention_days=$(yq eval '.backupRetentionDays' "$config_file")
        if [[ -n "$retention_days" && "$retention_days" =~ ^[0-9]+$ ]]; then
            if [[ $retention_days -lt 1 || $retention_days -gt 365 ]]; then
                log_warning "Backup retention days ($retention_days) should be between 1 and 365"
            fi
        fi
    fi
    
    # Validate advanced configuration if present
    if [[ $(yq eval 'has("advanced")' "$config_file") == "true" ]]; then
        validate_yaml_advanced_config "$config_file"
    fi
}

# Function to validate advanced YAML configuration
validate_yaml_advanced_config() {
    local config_file=$1
    log_debug "Validating advanced YAML configuration"
    
    # Validate PM2 instances
    if [[ $(yq eval 'has("advanced.pm2Instances")' "$config_file") == "true" ]]; then
        local pm2_instances=$(yq eval '.advanced.pm2Instances' "$config_file")
        if [[ -n "$pm2_instances" && "$pm2_instances" != "max" ]]; then
            if ! [[ "$pm2_instances" =~ ^[0-9]+$ ]] || [[ $pm2_instances -lt 1 || $pm2_instances -gt 16 ]]; then
                log_warning "PM2 instances should be 'max' or a number between 1 and 16, got: $pm2_instances"
            fi
        fi
    fi
    
    # Validate memory settings
    if [[ $(yq eval 'has("advanced.maxMemoryRestart")' "$config_file") == "true" ]]; then
        local max_memory=$(yq eval '.advanced.maxMemoryRestart' "$config_file")
        if [[ -n "$max_memory" && ! "$max_memory" =~ ^[0-9]+[KMG]?$ ]]; then
            log_warning "Invalid memory format: $max_memory. Use format like '1G', '512M', '1024K'"
        fi
    fi
    
    # Validate rate limiting values
    local rate_limit_fields=("nginxRateLimitApi" "nginxRateLimitAuth" "nginxRateLimitGeneral")
    for field in "${rate_limit_fields[@]}"; do
        if [[ $(yq eval "has(\"advanced.$field\")" "$config_file") == "true" ]]; then
            local rate_limit=$(yq eval ".advanced.$field" "$config_file")
            if [[ -n "$rate_limit" && ! "$rate_limit" =~ ^[0-9]+r/[sm]$ ]]; then
                log_warning "Invalid rate limit format for $field: $rate_limit. Use format like '10r/s' or '100r/m'"
            fi
        fi
    done
}

# Function to extract YAML configuration values with enhanced error handling
extract_yaml_values() {
    local config_file=$1
    log_debug "Extracting configuration values from YAML"
    
    # Helper function to safely extract YAML values
    safe_yq_extract() {
        local path=$1
        local default_value=${2:-""}
        local result
        
        result=$(yq eval "$path // \"$default_value\"" "$config_file" 2>/dev/null)
        
        # Handle null values
        if [[ "$result" == "null" ]]; then
            result="$default_value"
        fi
        
        echo "$result"
    }
    
    # Extract basic configuration with validation
    DOMAIN_NAME=$(safe_yq_extract '.domainName')
    SSL_MODE=$(safe_yq_extract '.sslMode' 'cloudflare')
    DB_PASSWORD=$(safe_yq_extract '.dbPassword')
    JWT_SECRET=$(safe_yq_extract '.jwtSecret')
    ADMIN_USERNAME=$(safe_yq_extract '.adminUsername' 'admin')
    ADMIN_PASSWORD=$(safe_yq_extract '.adminPassword')
    ADMIN_EMAIL=$(safe_yq_extract '.adminEmail')
    REPO_URL=$(safe_yq_extract '.repoUrl')
    REPO_BRANCH=$(safe_yq_extract '.repoBranch' 'main')
    ENV_MODE=$(safe_yq_extract '.environment' 'production')
    
    # Convert boolean strings to proper boolean values
    MONITORING_ENABLED=$(convert_to_boolean "$(safe_yq_extract '.monitoringEnabled' 'true')")
    BACKUP_ENABLED=$(convert_to_boolean "$(safe_yq_extract '.backupEnabled' 'true')")
    
    # Extract numeric values with validation
    BACKUP_RETENTION_DAYS=$(safe_yq_extract '.backupRetentionDays' '7')
    
    # Validate extracted values
    if [[ -z "$DOMAIN_NAME" ]]; then
        log_error "Domain name is required but not specified in configuration"
        exit 1
    fi
    
    log_debug "Basic configuration extracted successfully"
    
    # Extract advanced configuration if present
    if [[ $(yq eval 'has("advanced")' "$config_file") == "true" ]]; then
        extract_yaml_advanced_values "$config_file"
    fi
    
    # Extract Cloudflare configuration if present
    if [[ $(yq eval 'has("cloudflare")' "$config_file") == "true" ]]; then
        extract_yaml_cloudflare_values "$config_file"
    fi
}

# Function to convert string values to boolean
convert_to_boolean() {
    local value=$(echo "$1" | tr '[:upper:]' '[:lower:]')
    
    case "$value" in
        "true"|"yes"|"1"|"on"|"enabled")
            echo "true"
            ;;
        "false"|"no"|"0"|"off"|"disabled"|"")
            echo "false"
            ;;
        *)
            log_warning "Ambiguous boolean value '$1', treating as false"
            echo "false"
            ;;
    esac
}

# Function to extract advanced YAML configuration values
extract_yaml_advanced_values() {
    local config_file=$1
    log_debug "Extracting advanced configuration values"
    
    # Helper function for advanced config extraction
    safe_advanced_extract() {
        local path=$1
        local default_value=${2:-""}
        yq eval ".advanced.$path // \"$default_value\"" "$config_file" 2>/dev/null
    }
    
    # Server configuration
    NODE_VERSION=$(safe_advanced_extract 'nodeVersion' '18')
    POSTGRES_VERSION=$(safe_advanced_extract 'postgresVersion' '15')
    APP_USER=$(safe_advanced_extract 'appUser' 'hauling_app')
    APP_DIR=$(safe_advanced_extract 'appDir' '/var/www/hauling-qr-system')
    
    # Performance settings
    PM2_INSTANCES=$(safe_advanced_extract 'pm2Instances' 'max')
    MAX_MEMORY_RESTART=$(safe_advanced_extract 'maxMemoryRestart' '2G')
    NODE_MAX_OLD_SPACE=$(safe_advanced_extract 'nodeMaxOldSpace' '2048')
    
    # Security settings
    FAIL2BAN_BANTIME=$(safe_advanced_extract 'fail2banBantime' '3600')
    FAIL2BAN_MAXRETRY=$(safe_advanced_extract 'fail2banMaxretry' '8')
    UFW_ENABLE=$(convert_to_boolean "$(safe_advanced_extract 'ufwEnable' 'true')")
    
    # SSL configuration
    SSL_CERT_PATH=$(safe_advanced_extract 'sslCertPath')
    SSL_KEY_PATH=$(safe_advanced_extract 'sslKeyPath')
    SSL_COUNTRY=$(safe_advanced_extract 'sslCountry' 'US')
    SSL_STATE=$(safe_advanced_extract 'sslState' 'State')
    SSL_CITY=$(safe_advanced_extract 'sslCity' 'City')
    SSL_ORG=$(safe_advanced_extract 'sslOrg' 'Hauling QR System')
    
    # Rate limiting
    NGINX_RATE_LIMIT_API=$(safe_advanced_extract 'nginxRateLimitApi' '20r/s')
    NGINX_RATE_LIMIT_AUTH=$(safe_advanced_extract 'nginxRateLimitAuth' '5r/s')
    NGINX_RATE_LIMIT_GENERAL=$(safe_advanced_extract 'nginxRateLimitGeneral' '10r/s')
    
    # Database optimization
    DB_SHARED_BUFFERS=$(safe_advanced_extract 'dbSharedBuffers' '256MB')
    DB_EFFECTIVE_CACHE_SIZE=$(safe_advanced_extract 'dbEffectiveCacheSize' '1GB')
    DB_WORK_MEM=$(safe_advanced_extract 'dbWorkMem' '4MB')
    DB_MAX_CONNECTIONS=$(safe_advanced_extract 'dbMaxConnections' '100')
    
    # Monitoring configuration
    HEALTH_CHECK_INTERVAL=$(safe_advanced_extract 'healthCheckInterval' '*/5 * * * *')
    PERFORMANCE_CHECK_INTERVAL=$(safe_advanced_extract 'performanceCheckInterval' '*/10 * * * *')
    REPORT_GENERATION_TIME=$(safe_advanced_extract 'reportGenerationTime' '0 6 * * *')
    
    # Backup configuration
    FULL_BACKUP_SCHEDULE=$(safe_advanced_extract 'fullBackupSchedule' '0 3 * * 0')
    BACKUP_COMPRESSION=$(convert_to_boolean "$(safe_advanced_extract 'backupCompression' 'true')")
    
    # Logging configuration
    LOG_LEVEL=$(safe_advanced_extract 'logLevel' 'info')
    LOG_ROTATION_SIZE=$(safe_advanced_extract 'logRotationSize' '100M')
    LOG_RETENTION_DAYS=$(safe_advanced_extract 'logRetentionDays' '52')
    
    # Email configuration
    SMTP_HOST=$(safe_advanced_extract 'smtpHost')
    SMTP_PORT=$(safe_advanced_extract 'smtpPort' '587')
    SMTP_USER=$(safe_advanced_extract 'smtpUser')
    SMTP_PASSWORD=$(safe_advanced_extract 'smtpPassword')
    ALERT_EMAIL=$(safe_advanced_extract 'alertEmail')
    
    log_debug "Advanced configuration extracted successfully"
}

# Function to extract Cloudflare YAML configuration values
extract_yaml_cloudflare_values() {
    local config_file=$1
    log_debug "Extracting Cloudflare configuration values"
    
    # Helper function for Cloudflare config extraction
    safe_cf_extract() {
        local path=$1
        local default_value=${2:-""}
        yq eval ".cloudflare.$path // \"$default_value\"" "$config_file" 2>/dev/null
    }
    
    # Cloudflare configuration
    CLOUDFLARE_SSL_MODE=$(safe_cf_extract 'sslMode' 'full')
    CLOUDFLARE_API_TOKEN=$(safe_cf_extract 'apiToken')
    CLOUDFLARE_ZONE_ID=$(safe_cf_extract 'zoneId')
    CF_MINIFY_HTML=$(convert_to_boolean "$(safe_cf_extract 'minifyHtml' 'true')")
    CF_MINIFY_CSS=$(convert_to_boolean "$(safe_cf_extract 'minifyCss' 'true')")
    CF_MINIFY_JS=$(convert_to_boolean "$(safe_cf_extract 'minifyJs' 'true')")
    CF_BROTLI=$(convert_to_boolean "$(safe_cf_extract 'brotli' 'true')")
    CF_CACHE_LEVEL=$(safe_cf_extract 'cacheLevel' 'standard')
    CF_BROWSER_CACHE_TTL=$(safe_cf_extract 'browserCacheTtl' '14400')
    
    log_debug "Cloudflare configuration extracted successfully"
}

# Function to generate YAML configuration templateon template
generate_yaml_config_template() {
    local output_file=${1:-"deployment-config-template.yaml"}
    
    log_info "Generating YAML configuration template: $output_file"
    
    cat > "$output_file" << 'EOF'
# ============================================================================
# HAULING QR TRIP SYSTEM - DEPLOYMENT CONFIGURATION TEMPLATE (YAML)
# ============================================================================
# Copy this file and customize for your deployment
# Usage: ./deploy-hauling-qr-ubuntu.sh --config your-config.yaml
#
# This template includes all available configuration options with examples
# and documentation. Remove or comment out sections you don't need.
# ============================================================================

# ============================================================================
# BASIC CONFIGURATION (Required)
# ============================================================================

# Domain name for your deployment (REQUIRED)
domainName: "truckhaul.top"

# SSL/TLS configuration mode
# Options: none, letsencrypt, cloudflare, custom
sslMode: "cloudflare"

# Deployment environment
# Options: production, staging, development
environment: "production"

# ============================================================================
# AUTHENTICATION & SECURITY
# ============================================================================

# Database password (leave empty for auto-generation)
dbPassword: ""

# JWT secret for authentication (leave empty for auto-generation)
jwtSecret: ""

# Admin user configuration
adminUsername: "admin"
adminPassword: ""  # Leave empty for auto-generation
adminEmail: "<EMAIL>"

# ============================================================================
# APPLICATION CONFIGURATION
# ============================================================================

# Git repository configuration
repoUrl: "https://github.com/mightybadz18/hauling-qr-trip-management.git"
repoBranch: "main"

# Feature toggles
monitoringEnabled: true
backupEnabled: true
backupRetentionDays: 7

# ============================================================================
# ADVANCED CONFIGURATION (Optional)
# ============================================================================

advanced:
  # Server Configuration
  nodeVersion: "18"
  postgresVersion: "15"
  appUser: "hauling_app"
  appDir: "/var/www/hauling-qr-system"
  
  # Performance Settings
  pm2Instances: "max"          # Options: "max" or number (e.g., 4)
  maxMemoryRestart: "2G"       # PM2 memory restart threshold
  nodeMaxOldSpace: 2048        # Node.js heap size in MB
  
  # Security Settings
  fail2banBantime: 3600        # Ban time in seconds
  fail2banMaxretry: 8          # Max retry attempts before ban
  ufwEnable: true              # Enable UFW firewall
  
  # SSL Configuration (for custom SSL mode)
  sslCertPath: "/path/to/certificate.crt"
  sslKeyPath: "/path/to/private.key"
  sslCountry: "US"
  sslState: "State"
  sslCity: "City"
  sslOrg: "Your Organization"
  
  # Rate Limiting Configuration
  nginxRateLimitApi: "20r/s"     # API rate limit
  nginxRateLimitAuth: "5r/s"     # Authentication rate limit
  nginxRateLimitGeneral: "10r/s" # General rate limit
  
  # Database Optimization
  dbSharedBuffers: "256MB"       # PostgreSQL shared buffers
  dbEffectiveCacheSize: "1GB"    # PostgreSQL effective cache size
  dbWorkMem: "4MB"               # PostgreSQL work memory
  dbMaxConnections: 100          # PostgreSQL max connections
  
  # Monitoring Configuration
  healthCheckInterval: "*/5 * * * *"    # Cron format: every 5 minutes
  performanceCheckInterval: "*/10 * * * *"  # Cron format: every 10 minutes
  reportGenerationTime: "0 6 * * *"     # Cron format: daily at 6 AM
  
  # Backup Configuration
  fullBackupSchedule: "0 3 * * 0"       # Weekly full backup on Sunday at 3 AM
  backupCompression: true               # Compress backups
  
  # Logging Configuration
  logLevel: "info"                      # Options: error, warn, info, debug
  logRotationSize: "100M"               # Rotate logs when they reach this size
  logRetentionDays: 52                  # Keep logs for 52 days
  
  # Email Configuration (for alerts)
  smtpHost: "smtp.gmail.com"
  smtpPort: 587
  smtpUser: "<EMAIL>"
  smtpPassword: "your_smtp_password"
  alertEmail: "<EMAIL>"

# ============================================================================
# CLOUDFLARE SPECIFIC SETTINGS (Optional)
# ============================================================================

cloudflare:
  # Cloudflare SSL mode
  # Options: flexible, full, full_strict
  sslMode: "full"
  
  # Cloudflare API credentials (optional, for advanced SSL management)
  apiToken: ""                          # Leave empty if not using API
  zoneId: ""                           # Leave empty if not using API
  
  # Cloudflare Optimization Settings
  minifyHtml: true
  minifyCss: true
  minifyJs: true
  brotli: true
  cacheLevel: "standard"
  browserCacheTtl: 14400                # 4 hours in seconds

# ============================================================================
# EXAMPLES OF DIFFERENT CONFIGURATIONS
# ============================================================================

# Example 1: Minimal Production Configuration
# domainName: "myapp.example.com"
# sslMode: "letsencrypt"
# environment: "production"
# adminEmail: "<EMAIL>"

# Example 2: Development Configuration
# domainName: "dev.example.com"
# sslMode: "none"
# environment: "development"
# monitoringEnabled: false
# backupEnabled: false

# Example 3: Staging with Custom SSL
# domainName: "staging.example.com"
# sslMode: "custom"
# environment: "staging"
# advanced:
#   sslCertPath: "/etc/ssl/certs/staging.crt"
#   sslKeyPath: "/etc/ssl/private/staging.key"

# ============================================================================
# VALIDATION NOTES
# ============================================================================
# 
# - domainName is required and must be a valid domain format
# - sslMode must be one of: none, letsencrypt, cloudflare, custom
# - environment must be one of: production, staging, development
# - Boolean values can be: true/false, yes/no, 1/0, on/off, enabled/disabled
# - Numeric values should be provided as numbers or quoted strings
# - Cron expressions should be valid cron format
# - Email addresses should be valid email format
# - File paths should be absolute paths for SSL certificates
# 
# ============================================================================
EOF
    
    log_success "YAML configuration template generated: $output_file"
    log_info "Edit this file with your specific configuration and use it with:"
    log_info "  ./deploy-hauling-qr-ubuntu.sh --config $output_file"
}
# Function to validate domain name format
validate_domain_name() {
    local domain=$1
    
    # Check if domain is empty
    if [[ -z "$domain" ]]; then
        return 1
    fi
    
    # Check domain length (max 253 characters)
    if [[ ${#domain} -gt 253 ]]; then
        log_error "Domain name too long (${#domain} characters, max 253)"
        return 1
    fi
    
    # Check for valid domain format using regex
    # This regex allows for standard domain names including subdomains
    local domain_regex='^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$'
    
    if [[ ! "$domain" =~ $domain_regex ]]; then
        log_error "Invalid domain name format: $domain"
        log_error "Domain names must:"
        log_error "  - Contain only letters, numbers, and hyphens"
        log_error "  - Not start or end with hyphens"
        log_error "  - Have valid TLD (top-level domain)"
        log_error "  - Not exceed 63 characters per label"
        return 1
    fi
    
    # Check for reserved/invalid domains
    case "$domain" in
        "localhost"|"127.0.0.1"|"::1")
            log_error "Cannot use localhost or loopback addresses as domain"
            return 1
            ;;
        *.local|*.localhost)
            log_warning "Using .local or .localhost domain may cause issues with SSL certificates"
            ;;
        *.test|*.example|*.invalid)
            log_warning "Using reserved TLD (.test, .example, .invalid) - this may be intentional for testing"
            ;;
    esac
    
    # Check if domain has at least one dot (TLD requirement)
    if [[ ! "$domain" =~ \. ]]; then
        log_error "Domain must include a top-level domain (TLD)"
        return 1
    fi
    
    return 0
}
# ============================================================================
# CI/CD FRIENDLY OUTPUT FUNCTIONS
# ============================================================================

# Global variables for CI/CD output
CI_CD_MODE=false
OUTPUT_FORMAT="text"  # Options: text, json, yaml
QUIET_MODE=false
PROGRESS_INDICATORS=true
STRUCTURED_OUTPUT_FILE=""

# Function to enable CI/CD mode
enable_cicd_mode() {
    CI_CD_MODE=true
    CONSOLE_OUTPUT=false
    PROGRESS_INDICATORS=false
    log_info "CI/CD mode enabled"
}

# Function to set output format
set_output_format() {
    local format=$1
    case "$format" in
        "text"|"json"|"yaml")
            OUTPUT_FORMAT="$format"
            log_debug "Output format set to: $format"
            ;;
        *)
            log_error "Invalid output format: $format. Valid options: text, json, yaml"
            exit 1
            ;;
    esac
}

# Function to enable quiet mode
enable_quiet_mode() {
    QUIET_MODE=true
    CONSOLE_OUTPUT=false
    CURRENT_LOG_LEVEL=$LOG_LEVEL_ERROR
    log_debug "Quiet mode enabled"
}

# Function to output structured data
output_structured_data() {
    local data_type=$1
    local data=$2
    local timestamp=$(date '+%Y-%m-%dT%H:%M:%S.%3N%z')
    
    case "$OUTPUT_FORMAT" in
        "json")
            output_json_data "$data_type" "$data" "$timestamp"
            ;;
        "yaml")
            output_yaml_data "$data_type" "$data" "$timestamp"
            ;;
        "text")
            output_text_data "$data_type" "$data" "$timestamp"
            ;;
    esac
}

# Function to output JSON formatted data
output_json_data() {
    local data_type=$1
    local data=$2
    local timestamp=$3
    
    # Create JSON structure
    local json_output=$(cat << EOF
{
  "timestamp": "$timestamp",
  "type": "$data_type",
  "hostname": "$(hostname)",
  "deployment": {
    "domain": "$DOMAIN_NAME",
    "environment": "$ENV_MODE",
    "version": "$VERSION"
  },
  "data": $data
}
EOF
)
    
    # Output to file or stdout
    if [[ -n "$STRUCTURED_OUTPUT_FILE" ]]; then
        echo "$json_output" >> "$STRUCTURED_OUTPUT_FILE"
    else
        echo "$json_output"
    fi
}

# Function to output YAML formatted data
output_yaml_data() {
    local data_type=$1
    local data=$2
    local timestamp=$3
    
    # Create YAML structure
    local yaml_output=$(cat << EOF
---
timestamp: "$timestamp"
type: "$data_type"
hostname: "$(hostname)"
deployment:
  domain: "$DOMAIN_NAME"
  environment: "$ENV_MODE"
  version: "$VERSION"
data: $data
EOF
)
    
    # Output to file or stdout
    if [[ -n "$STRUCTURED_OUTPUT_FILE" ]]; then
        echo "$yaml_output" >> "$STRUCTURED_OUTPUT_FILE"
    else
        echo "$yaml_output"
    fi
}

# Function to output text formatted data
output_text_data() {
    local data_type=$1
    local data=$2
    local timestamp=$3
    
    # Create text structure
    local text_output="[$timestamp] $data_type: $data"
    
    # Output to file or stdout
    if [[ -n "$STRUCTURED_OUTPUT_FILE" ]]; then
        echo "$text_output" >> "$STRUCTURED_OUTPUT_FILE"
    else
        echo "$text_output"
    fi
}

# Function to output deployment progress for CI/CD
output_deployment_progress() {
    local step=$1
    local status=$2  # started, completed, failed
    local message=${3:-""}
    local percentage=${4:-0}
    
    if [[ "$CI_CD_MODE" == true ]]; then
        local progress_data=$(cat << EOF
{
  "step": "$step",
  "status": "$status",
  "message": "$message",
  "percentage": $percentage,
  "current_step": "$current_step"
}
EOF
)
        output_structured_data "progress" "$progress_data"
    fi
}

# Function to output deployment result for CI/CD
output_deployment_result() {
    local success=$1
    local exit_code=${2:-0}
    local summary=${3:-""}
    local duration=${4:-0}
    
    local result_data=$(cat << EOF
{
  "success": $success,
  "exit_code": $exit_code,
  "summary": "$summary",
  "duration_seconds": $duration,
  "deployment_time": "$(date '+%Y-%m-%d %H:%M:%S')",
  "access_urls": {
    "frontend": "https://$DOMAIN_NAME",
    "admin": "https://$DOMAIN_NAME/admin",
    "api": "https://$DOMAIN_NAME/api",
    "health": "https://$DOMAIN_NAME/api/health"
  }
}
EOF
)
    
    output_structured_data "result" "$result_data"
}

# Function to output system information for CI/CD
output_system_info() {
    local system_data=$(cat << EOF
{
  "os": "$(lsb_release -d 2>/dev/null | cut -f2- || cat /etc/os-release | grep PRETTY_NAME | cut -d'\"' -f2)",
  "kernel": "$(uname -r)",
  "architecture": "$(uname -m)",
  "cpu_cores": $(nproc),
  "memory_total": "$(free -h | grep Mem | awk '{print $2}')",
  "memory_available": "$(free -h | grep Mem | awk '{print $4}')",
  "disk_total": "$(df -h / | awk 'NR==2 {print $2}')",
  "disk_available": "$(df -h / | awk 'NR==2 {print $4}')",
  "ip_address": "$(hostname -I | awk '{print $1}')"
}
EOF
)
    
    output_structured_data "system_info" "$system_data"
}

# Function to output configuration summary for CI/CD
output_config_summary() {
    local config_data=$(cat << EOF
{
  "domain": "$DOMAIN_NAME",
  "ssl_mode": "$SSL_MODE",
  "environment": "$ENV_MODE",
  "monitoring_enabled": $MONITORING_ENABLED,
  "backup_enabled": $BACKUP_ENABLED,
  "backup_retention_days": $BACKUP_RETENTION_DAYS,
  "admin_username": "$ADMIN_USERNAME",
  "admin_email": "$ADMIN_EMAIL",
  "repository_url": "$REPO_URL",
  "repository_branch": "$REPO_BRANCH"
}
EOF
)
    
    output_structured_data "configuration" "$config_data"
}

# Function to output error information for CI/CD
output_error_info() {
    local error_code=$1
    local error_message=$2
    local error_step=${3:-"$current_step"}
    local error_line=${4:-""}
    
    local error_data=$(cat << EOF
{
  "error_code": $error_code,
  "error_message": "$error_message",
  "error_step": "$error_step",
  "error_line": "$error_line",
  "timestamp": "$(date '+%Y-%m-%d %H:%M:%S')",
  "troubleshooting": {
    "log_file": "$LOG_FILE",
    "error_report": "/tmp/hauling-deployment-error.log",
    "suggested_action": "Check the error report and logs for detailed information"
  }
}
EOF
)
    
    output_structured_data "error" "$error_data"
}

# Function to show progress indicator (CI/CD compatible)
show_progress() {
    local message=$1
    local current=${2:-0}
    local total=${3:-100}
    
    if [[ "$PROGRESS_INDICATORS" == true && "$QUIET_MODE" != true ]]; then
        if [[ "$CI_CD_MODE" == true ]]; then
            # CI/CD friendly progress output
            local percentage=$((current * 100 / total))
            output_deployment_progress "$message" "in_progress" "$message" "$percentage"
        else
            # Interactive progress display
            local percentage=$((current * 100 / total))
            local bar_length=50
            local filled_length=$((percentage * bar_length / 100))
            local bar=$(printf "%*s" "$filled_length" | tr ' ' '█')
            local empty=$(printf "%*s" $((bar_length - filled_length)) | tr ' ' '░')
            
            printf "\r\033[K%s [%s%s] %d%%" "$message" "$bar" "$empty" "$percentage"
            
            if [[ $current -eq $total ]]; then
                echo  # New line when complete
            fi
        fi
    fi
}

# Function to output deployment metrics for CI/CD
output_deployment_metrics() {
    local start_time=$1
    local end_time=$2
    local success=$3
    
    local duration=$((end_time - start_time))
    local hours=$((duration / 3600))
    local minutes=$(( (duration % 3600) / 60 ))
    local seconds=$((duration % 60))
    
    local metrics_data=$(cat << EOF
{
  "start_time": $(date -d @$start_time '+%s'),
  "end_time": $(date -d @$end_time '+%s'),
  "duration_seconds": $duration,
  "duration_formatted": "$(printf "%02d:%02d:%02d" $hours $minutes $seconds)",
  "success": $success,
  "steps_completed": $(get_completed_steps_count),
  "system_metrics": {
    "cpu_usage": "$(grep 'cpu ' /proc/stat | awk '{usage=($2+$4)*100/($2+$4+$5)} END {print usage "%"}')",
    "memory_usage": "$(free | grep Mem | awk '{print $3/$2 * 100.0 "%"}')",
    "disk_usage": "$(df -h / | awk 'NR==2 {print $5}')"
  }
}
EOF
)
    
    output_structured_data "metrics" "$metrics_data"
}

# Function to get count of completed steps
get_completed_steps_count() {
    # This would track completed deployment steps
    # For now, return a placeholder
    echo "0"
}

# Function to initialize CI/CD output
initialize_cicd_output() {
    if [[ "$CI_CD_MODE" == true ]]; then
        log_info "Initializing CI/CD output mode"
        
        # Output initial system information
        output_system_info
        
        # Output configuration summary
        output_config_summary
        
        # Set up structured output file if specified
        if [[ -n "$STRUCTURED_OUTPUT_FILE" ]]; then
            # Create output file and add header
            case "$OUTPUT_FORMAT" in
                "json")
                    echo '{"deployment_log":[' > "$STRUCTURED_OUTPUT_FILE"
                    ;;
                "yaml")
                    echo '---' > "$STRUCTURED_OUTPUT_FILE"
                    echo 'deployment_log:' >> "$STRUCTURED_OUTPUT_FILE"
                    ;;
            esac
        fi
    fi
}

# Function to finalize CI/CD output
finalize_cicd_output() {
    if [[ "$CI_CD_MODE" == true && -n "$STRUCTURED_OUTPUT_FILE" ]]; then
        case "$OUTPUT_FORMAT" in
            "json")
                # Close JSON array
                echo ']}' >> "$STRUCTURED_OUTPUT_FILE"
                ;;
            "yaml")
                # YAML doesn't need closing
                echo '...' >> "$STRUCTURED_OUTPUT_FILE"
                ;;
        esac
        
        log_info "Structured output saved to: $STRUCTURED_OUTPUT_FILE"
    fi
}

# ============================================================================
# EXIT CODES FOR CI/CD INTEGRATION
# ============================================================================

# Define exit codes for different failure scenarios
readonly EXIT_SUCCESS=0
readonly EXIT_GENERAL_ERROR=1
readonly EXIT_CONFIG_ERROR=2
readonly EXIT_NETWORK_ERROR=3
readonly EXIT_PERMISSION_ERROR=4
readonly EXIT_DEPENDENCY_ERROR=5
readonly EXIT_DATABASE_ERROR=6
readonly EXIT_SSL_ERROR=7
readonly EXIT_SERVICE_ERROR=8
readonly EXIT_VALIDATION_ERROR=9
readonly EXIT_TIMEOUT_ERROR=10
readonly EXIT_DISK_SPACE_ERROR=11
readonly EXIT_MEMORY_ERROR=12
readonly EXIT_USER_CANCELLED=13
readonly EXIT_INCOMPATIBLE_SYSTEM=14
readonly EXIT_BACKUP_ERROR=15

# Function to exit with appropriate code and CI/CD output
exit_with_code() {
    local exit_code=$1
    local error_message=${2:-"Unknown error"}
    local error_step=${3:-"$current_step"}
    
    # Output error information for CI/CD
    if [[ "$CI_CD_MODE" == true ]]; then
        output_error_info "$exit_code" "$error_message" "$error_step"
        
        # Output final deployment result
        local duration=$(($(date +%s) - DEPLOYMENT_START_TIME))
        output_deployment_result "false" "$exit_code" "$error_message" "$duration"
        
        # Finalize CI/CD output
        finalize_cicd_output
    fi
    
    # Log the exit
    log_error "Deployment failed with exit code $exit_code: $error_message"
    
    exit "$exit_code"
}

# Function to get exit code description
get_exit_code_description() {
    local code=$1
    
    case $code in
        $EXIT_SUCCESS)
            echo "Success"
            ;;
        $EXIT_GENERAL_ERROR)
            echo "General error"
            ;;
        $EXIT_CONFIG_ERROR)
            echo "Configuration error"
            ;;
        $EXIT_NETWORK_ERROR)
            echo "Network connectivity error"
            ;;
        $EXIT_PERMISSION_ERROR)
            echo "Permission denied error"
            ;;
        $EXIT_DEPENDENCY_ERROR)
            echo "Dependency installation error"
            ;;
        $EXIT_DATABASE_ERROR)
            echo "Database setup error"
            ;;
        $EXIT_SSL_ERROR)
            echo "SSL/TLS configuration error"
            ;;
        $EXIT_SERVICE_ERROR)
            echo "Service startup error"
            ;;
        $EXIT_VALIDATION_ERROR)
            echo "Validation error"
            ;;
        $EXIT_TIMEOUT_ERROR)
            echo "Operation timeout error"
            ;;
        $EXIT_DISK_SPACE_ERROR)
            echo "Insufficient disk space error"
            ;;
        $EXIT_MEMORY_ERROR)
            echo "Insufficient memory error"
            ;;
        $EXIT_USER_CANCELLED)
            echo "User cancelled operation"
            ;;
        $EXIT_INCOMPATIBLE_SYSTEM)
            echo "Incompatible system error"
            ;;
        $EXIT_BACKUP_ERROR)
            echo "Backup operation error"
            ;;
        *)
            echo "Unknown error code"
            ;;
    esac
}

# Enhanced error handler with CI/CD support
handle_error_cicd() {
    local exit_code=$1
    local line_number=$2
    local error_step="$current_step"
    
    # Determine appropriate exit code based on current step
    local cicd_exit_code=$EXIT_GENERAL_ERROR
    
    case $current_step in
        "system_update"|"dependencies_installation")
            cicd_exit_code=$EXIT_DEPENDENCY_ERROR
            ;;
        "nginx_config"|"ssl_setup")
            cicd_exit_code=$EXIT_SSL_ERROR
            ;;
        "database_setup")
            cicd_exit_code=$EXIT_DATABASE_ERROR
            ;;
        "repository_clone"|"npm_install")
            cicd_exit_code=$EXIT_NETWORK_ERROR
            ;;
        "firewall_setup"|"fail2ban_setup")
            cicd_exit_code=$EXIT_PERMISSION_ERROR
            ;;
        "pm2_setup")
            cicd_exit_code=$EXIT_SERVICE_ERROR
            ;;
        "deployment_verification")
            cicd_exit_code=$EXIT_VALIDATION_ERROR
            ;;
        *)
            cicd_exit_code=$EXIT_GENERAL_ERROR
            ;;
    esac
    
    # Get error description
    local error_description=$(get_exit_code_description $cicd_exit_code)
    local error_message="$error_description on line $line_number"
    
    # Call original error handler for detailed logging
    handle_error "$exit_code" "$line_number"
    
    # Exit with appropriate code for CI/CD
    if [[ "$CI_CD_MODE" == true ]]; then
        exit_with_code "$cicd_exit_code" "$error_message" "$error_step"
    fi
}

# Function to show available exit codes
show_exit_codes() {
    cat << EOF
Hauling QR Trip Management System Deployment Script - Exit Codes

The deployment script uses specific exit codes to indicate different types of failures.
This is particularly useful for CI/CD pipelines and automated deployments.

Exit Codes:
  $EXIT_SUCCESS              Success - Deployment completed successfully
  $EXIT_GENERAL_ERROR              General error - Unspecified error occurred
  $EXIT_CONFIG_ERROR              Configuration error - Invalid or missing configuration
  $EXIT_NETWORK_ERROR              Network error - Connectivity issues or download failures
  $EXIT_PERMISSION_ERROR              Permission error - Insufficient permissions
  $EXIT_DEPENDENCY_ERROR              Dependency error - Failed to install required packages
  $EXIT_DATABASE_ERROR              Database error - Database setup or connection failed
  $EXIT_SSL_ERROR              SSL/TLS error - Certificate or SSL configuration failed
  $EXIT_SERVICE_ERROR              Service error - Failed to start or configure services
  $EXIT_VALIDATION_ERROR              Validation error - Deployment verification failed
  $EXIT_TIMEOUT_ERROR             Timeout error - Operation timed out
  $EXIT_DISK_SPACE_ERROR             Disk space error - Insufficient disk space
  $EXIT_MEMORY_ERROR             Memory error - Insufficient memory
  $EXIT_USER_CANCELLED             User cancelled - User cancelled the operation
  $EXIT_INCOMPATIBLE_SYSTEM             Incompatible system - System requirements not met
  $EXIT_BACKUP_ERROR             Backup error - Backup operation failed

Usage in CI/CD:
  You can check the exit code in your CI/CD pipeline to determine the type of failure:
  
  # Example in GitHub Actions
  - name: Deploy Application
    run: ./deploy-hauling-qr-ubuntu.sh --config config.yaml --cicd-mode
    continue-on-error: true
    id: deploy
  
  - name: Handle Deployment Failure
    if: failure()
    run: |
      case \${{ steps.deploy.outputs.exit-code }} in
        $EXIT_CONFIG_ERROR) echo "Configuration error - check your config file" ;;
        $EXIT_NETWORK_ERROR) echo "Network error - check connectivity" ;;
        $EXIT_DATABASE_ERROR) echo "Database error - check database configuration" ;;
        *) echo "General deployment error" ;;
      esac

EOF
}

# ============================================================================
# DEPLOYMENT SCRIPT ENHANCEMENTS SUMMARY
# ============================================================================
#
# This enhanced version (v2.0.0) addresses all critical deployment failures:
#
# CATEGORY 1: MISSING CRITICAL FUNCTION DEFINITIONS - RESOLVED ✅
# - All functions (output_deployment_progress, show_progress, generate_strong_password)
#   are properly defined and functional
#
# CATEGORY 2: SYSTEMD/INIT SYSTEM COMPATIBILITY - RESOLVED ✅
# - Universal init system detection (systemd, SysV, upstart, none)
# - Fallback service management for Docker, WSL, and non-systemd environments
# - Process-based service checking using ps, pgrep, and netstat
# - Environment-specific configurations for Docker and WSL
#
# CATEGORY 3: NODE.JS ECOSYSTEM DEPENDENCIES - RESOLVED ✅
# - Comprehensive Node.js installation with multiple methods:
#   * NodeSource repository (primary)
#   * Snap package manager
#   * Node Version Manager (nvm)
#   * Binary download
#   * APT package manager (fallback)
# - Enhanced NPM verification and PM2 installation
# - Retry mechanisms with exponential backoff
#
# CATEGORY 4: FILESYSTEM AND DIRECTORY STRUCTURE - RESOLVED ✅
# - Safe directory navigation with validation
# - Repository cloning with conflict resolution and backup
# - Atomic directory operations with rollback capability
# - Working directory context maintenance
# - Path resolution for both absolute and relative paths
#
# CATEGORY 5: CONFIGURATION BACKUP WARNINGS - RESOLVED ✅
# - Enhanced PostgreSQL configuration detection with multiple methods
# - Graceful handling of missing configuration directories
# - Comprehensive backup verification and integrity checking
# - Alternative location searching for configuration files
#
# CATEGORY 6: ENHANCED ERROR RECOVERY - NEW ✅
# - Retry mechanisms with exponential backoff and jitter
# - Network connectivity checking with multiple methods
# - System issue detection and automatic fixing
# - Comprehensive prerequisites validation
# - Meaningful error messages with remediation suggestions
#
# CATEGORY 7: UNIVERSAL ENVIRONMENT COMPATIBILITY - NEW ✅
# - Ubuntu 18.04, 20.04, 22.04, 24.04 LTS support
# - Docker container compatibility (with and without systemd)
# - WSL1 and WSL2 environment support
# - VPS and bare metal installation compatibility
# - Privileged and unprivileged execution context support
#
# DEPLOYMENT TESTING RECOMMENDATIONS:
# 1. Test on Ubuntu 18.04 LTS (minimum supported version)
# 2. Test on Ubuntu 24.04 LTS (latest supported version)
# 3. Test in Docker container without systemd
# 4. Test in WSL2 environment
# 5. Test with existing conflicting services
# 6. Test with limited system resources
# 7. Test network connectivity issues and recovery
#
# TROUBLESHOOTING:
# - Check logs in /var/log/hauling-deployment/
# - Use --log-level debug for detailed information
# - Use --dry-run to validate configuration without changes
# - Check service status with enhanced service_is_active function
# - Verify environment detection with detect_environment function
#
# ============================================================================

# Call main function with all arguments
main "$@"