#!/bin/bash
#
# Performance and Security Validation Script
# Task 10.3 Implemeip Mion for Ubuntu Deploloymentpt
#
# This script  performance onfigura
#  or the Haulingrity constem deployment
       on"system deployment
#ipt version and metadata
VON="1.0.0
     ipt ME="Performance and Security Vation Script"
VERSION="1.0.0  "
    sars for output
RED='\0
# Colo'\033[0;32m'
YEL=uion [0;31m'3m'
BLUE='\0333[0;lm'
CYANOW='\033[36m'
NC='\033[0m' #   ecolor

# Test relp)        lor
ESTS_PASSED=
# Test results         "
FISTS_WARNING=0
TES    --report-f ;;

# Configuration
_NAME="${N_NAME:-"truckhaul.to
# L_MODE="${SSL_Muri--s;dflare"}"
P    N_NAME="$ST_DURATION=30l.top
   2hITY_SCAN_DEPTH="boudflare"}"
PERFRT_FILE="/tmp/perforN=30ity-repor%Y%m%d_%H%M%S).tx
S     --durati EPTH="bas"
REPOgging functi  p
log_header() {
# Logging functionYAN}============================"
    echo -e "${C$1${NC}"
         ;e "${CYAN}===========================
}

logction() {
    ech"\n${BLUE}--- $1 ---$"
}og_section [[ 
    echo -e rguand lE}--- $1 ---${NC}"
}og_test() {
# Comm   -e "${BLUE}[TEST]${"
    ((TESTS_T++))
}

log_
    echo -"${GREEN}[PASS]${NC} $1"
loC}"(TESTS_PASSED++)
}

log_fail
 T_ echo -e "${RED}tiAIL]${NC} $1"
    ecil() {
    %$TESTS_ess_rate})
    echo -e "$/ TESTSASSED * 10S1"
}   n() {
    echoL${NC${YELLOW}[WARN]${NC} $1ho -e "  e}"
    ((TESTS_WARNING++)ESngOW}WarniLL "  ${YE echo -e"
   FA(TESTS_WARNd: $TESTS_ ${RED}Fai "  echo -eC}"

log_info
"${BLUE}$1"
}

# Perfoalidation func
    yate_system_performance() {
    log_sectiion funcem Pensinerate Validation"
    
    up_syeader PU Performancev$VERSION"
 ring_systst "CPU Performance Ason
 e  log_incpu_cores=$(nproc)
    local loadDraity_csecuAIN_NAM -F'load average:' '{k '{print $1}' |//')
    
    log_info "CPU cores: $cpu_cores"
    log_averag"
    
    if ! eston t -vload_avg < $cpu_nes" | bc n
        log_pass "CPU loulator..."able ($load_avg < $"
    else
        log_warn "High nstaload detected & aptd_avg >= $cpu_cores)"
      ...c calculator b"Installingfo     log_in; then
    
    # Testte_system_redrformance
    validate "Memory Usage As
    vacal mem_total=$(STTERMAanceep M$2}')
    vocal mem_used=$(ioe:  log_infoe | awk '{print
    local mem_ucuri_percent=$(( mem_us/ mem_total ))
    val" validaturitysecormancetion
    vag_info onemoring_s${mem_total}MB"
    validate_bacAMry used: ${mem_usMB"
    log_info "Memory usage: ${m_percent}%"
    function cutomprehe eive report
    gf [[ te_performanercent -t]; then
      "Memory usage iseptable (${mem_ue_percent}
    elif [[ $mem_rynt -ltthen
    log log_warnTestSumm usage is high (${mem_ue_pert}%)"
    elseT_F==" >> "$======================================  echo "  
    echo -e_fail "Memos: ${age is criticalusage_percent}%)"
    fi
    echo -e "$"$RE datled: ${NC}$TESTS_FAILED"
    eT_est 3: Disk Perfornings:gurationcSTS_WAurity"
    log_test "Disk Performassment"
    local disk_usage=$(df -hESTS_PASnmp soft0 / TEST5}' | sed 's/%o LE"
   EPORo -e "${CYAN}Setrirmanate: ${NC}${emonitor ste}%"
    log_info "ge: $e}%"
    echo -e"\n${BLUE}t Loca${REPORT_FILE}"
    if [[ $diskt 80 ]
        log_pass "Disk usage ideacceptable (${sage}%)"
    iORT_FILE""sk_usage -lt 90 ]];rhen
        log_warn "DiskREEN}✅ is high (${disk_usNINssed
    elseIxit 0
        log_faisk usage is ccal (${disk_us
    "i
    
 $REPiTest 4: Netk Performance
 log_test "Network nectivity Asset"
    if pg -c 3 -W 5 8dev/null 2>&1; then
   erformanal ping "$REPOR:nction8.8.8 -c 3 | k -F'/' '{prin5}')
        lsy]-nfo "NetworkTS[[ency: ${ping_tis
    l   
      Ad===(echo "$ping_time 00" | bc -l) )); then
EO  # Test  log_pass "Network ls good (${ping_
        else "CPU P====rmance As====ent"
    lo      log_warn "Nes high (${ping_time}ms)"
    local load_avg=$===========wk -F'load==verage:' '{pri2}' | sed 's/')
    else
    if ((og_fail "Netad_avg < $gtivity test fc -l) 
    fi
}

va============_performanc=====
    logn "Nginx Perfmance Von"
    
    # Test 1: Ngnt"ation
    log_test "Notal=Configuratione";lidation"k '{prithena}')
    loco "mem_used=einx > /dev/nuep Mem | awv/
        if n"Nnx -t > /dent=$(( ma&1; then
            log_pass "figuration is valid"
    if [else
            loss "Memory ussconfiguratioagors"
        fi
        
        log_waro }'$5nt pri -nstalled - sk | awk configuration test/-c 3 8.8y: $(ping work Latenc}')
      
    
    inx Performance Settings
    #==_test  Diinx Performance Setts Check"
    localesri=formnf="/etc/ssmnx/nginx.conf"
    if [[ -f=k_usage=$onf" ]]; then======2 {print $5}'//')
     local worker_procees=$(grep -r_processesnx_conf" |  "#" | aprint $2}' | sed 's/;
        l) disk_rker_c {nections=$(grep -E "nections" "$nginxgrep -v "#" | int $2}' | sed )
        
    eliCorg_info "Worker proces]_processes"
        log_info "Worker cge is Uptim $worker_conne%)""
    una 
  d     if [[ "$ | ier_prage ses" == "au (" ]] || [[ "$wo-f2 |ull/esses" -geas$cpu_cores" ]];)
OS:os       log_pa==nx worker pcesses configured optprocesses)"
        ====
       est  log_warn stormay need optiorker_proce
S===log_te
        
        if [[ "$wg0 /S_connection 3 8.( TE8  ]]; then
  cces  og_pass "Nginx workeconnections configureadequately ($wtions)"
      gDFAIL
            log_pasn "Nginx worood (${pictions may need incnnections
        fi
  ta====
        log_warnx configuration file not f
     Summa================================
===     log_faiork connectivt failed"
U   fiTest 3: Gzip Compressi
    log_test "Ngipression Check"
nx_conf" ]] && grp on" "$nginx_con
        log_pass rfornx gzip {pressled"
    l===
        log_warn "Nggzip compressinot found or d
    fi
    
    if cst 4: SSL ngiformance
        if ng"SSL/TLS Performal 2>&1;ck"
    if [[ -f ogeecurityginx configrlable/hauling-qr-sysen
        llsesl_config/etc/nginailable/hauling-qr-m"
        
"      fif grsl_protocolsv1.3" "$ssl_en
      ss    log_pass "Modern oup d backrn "Lim configured"
pace)"l else
    fi log_warn "SSL/TLS protocol cfiguration 
        fi
      Te
        if grsed 's"ssl_ciphers" "$sssup_sfig"; then
            log_pass "SStc/nginx configurup"
    if [[lse
            log_rker_pSSL cipher conE guration n "$nginx"
        fi
  ss    
        log_warn  rker_ site configuratig_wnot found"
    fi    log_pass "inx worker processonfigured opty ($worker_proes)"
        else

validate_databnce() {
    lo  "Database Perfolidation"
        rker_connectio24 ]]; the
     bTest 1: PostgreSQL Snections ured
    log_test "PostgreSQL us"
    if e" ]]mctl is- "Nginx worker conne i"v/null 2>&1; thcrease ($workers)"
  }nfig_fi_pass "PostgreSQL s running"
    else
    l   log_fail "Postxon file ice is nong"
    fiturn
    fi
    
    log_test fginxx Ge Connection Pheck"
    log_test "Datanx_conl "repion Performance"
 abilical start_ "Nginxdate +%s%N)
    if sudpostgres psELECT 1;" > /dev 2>&1; then
        log_waend_time=ssiote +%s%N)
        local duration=$time - start_time) / 1000000 )) ert to millises
     ot fodump ng_warn "pg_ lo          else
# Tes   log_info "Database time: ${duration}ms"
    l   
        if [[ $durat="/ -lt 100 ]]; thn "Posable/hauling-qr-system  else
 le"[[ -f "$sog_pass "Database connection time is excell${duration}ms)"
        af gr[[ $duration -lt 500 ]]; then
            log_pass "Database conne protocosu is u p    "ation}ms)"
        else
            log_warn tSStabase connection tition date"${duration}ms)"
        fi
    else
        if grep -q atabase connecl_
            log_ss "SSL cipher confi
    
    # Test 3pheonabase Configuk
    log_acku "PostgreSQL Configuration Cheistsctop direckuog_pass "Ba     l     | wc -l)
    local pg_version=$(sudgres psql -t -c "S version();" 2>P 'PostgreSQL \K[ | head -1)
        lo]; $backupinx site configuration no
    if [[ -npg_version" 
        loconf="/etc/postgresql/ion/main/postgresql.conf
        
        if [aeploymeaulonf" ]]; then
       bacction "   "base _buffers
        al shared_bufferE "^shared_buffersg_conf" | aw3}' || ech)
            : uuinfo "Posterviced_buffers: $"
             ruostgreSQL Service Statut 
  e Sf      # Check max_connostgres
            local Postgr is rions=$(grep -E "^max_cions" "$pg_confprint $3}' | s|| echo 
            log_sBackup_stgreSQL max_cos: $max_contions"
        log_fail "PstebaeSQL service is not running"
val         log_pass stgreSQL conon accessible"
    fi
 f          log_waSQL configuration file not 
        fi
    log_test "Dnection Pmance"
    local startn "Could not deterabetsPostgreSQL versog_"
    fi
        local end_time=$%N)
        lo d: Database Sitiime) / ment
    log_ted (opatabase Size and iotoormance Asslsement"
    local db_size=$(son -lt postgres psql -t -c "Ssize_pretty(pgabase_size('h_system'));" 2>/dev/n | xargs)
    
    if [[ -n [[ $dur/dei]]; then
        log_infopass "Datacsize: $db_sizeood ($tion}ms)"
      lslog_pass "Datanformation retr
    else
        log_warn "uld not retrse size i
    fi
}

validateormance() {
    # Testction "ApQL ConfiguPerformance n"
   est "Postgronfiguration C
    # Test 1: Nrg rn=$(sudoSQL on Stosos
    log_test "Node.js Applicantatus"
    if commandpg_QL log rdev/null 2>&1; th_v
        local pm2_status=$( tc/logf  2>/dev/null | jq -rm2_env.status' 2dev/null || echon")
            lal shared_buff -E "^shed_buffers" "" | awk '{print| echo "128MB")
        log_info "Nodnectionplication sta_connectionsatus"
            
  "   loif [[ "$pm2_steSQs" == "onbushared_bthen
            log_inss "Node leSQL max_coon is runni$mavia PM2"
        else
            log_porn "Node.js applicatguraturations"
        fi
    else
     iilog_wat found - ch direct process
        if pgrep -f "noderver.js" /null; then
            log_"ould not .js appliQL ver is runnin
 isk    else
            l "Node.js n not detected"
    # //'t 4: Database Sit  {prin| ent
    log_test "Databas"Log Direcessment"
   isk Space size=$(sudo -u postgres  "SELECT pg_size_pretty(pg_ng_qr_syst);" 2>/deargs)
    # Test 2:"$dblication Re Time
    logg_int "Application Response Time"
    docal app_url=ase sizormaalhost:5000""
    
    if curl -s --mCould norie"$app_url" > /dev/null 2>en
        local rrl -s -w "%{time_tot" -o /devl --max-time 10 "$app_url" 2>
        local r -ull e_ms=$(echo "$og(du se_time * 1000" | bc | cut -d. -f1
        
        _applica "Ap_performancesponse time: ${rponse_ms}ms"
    log_sen "Applicrmance Validatio"
        if [[ $respon0 ]]; then
    )/lo    log_pass  Application response time is excellent ($s}ms)"
       _test "No$response_msatio 2000 ]]; then
    if co   log_pass qlg//va/nuln response te is good (${resse_ms}ms)"
        loca
            llog"ginx"Application       "e time is slow (response_ms}ms)"
        fi
    else
        else "Application not respondl $app_url"
           log_warn pplication status:pm2_stus"
    
    # Tystems Memory Usagdate Application
    lwarn "PM"Application Mfor Nodjage"
    locaep -f "erver.js(ps aux | g t"node.*server.js" | grep -wk '{sum+=$6}D {print sum/>/dev/null)
    
    ir  [ -n "$node_memory" ]] && [ode_memory"" ]]; then
            infon "Nodication memory usage:_memory}MB"
  resenter
        if (("$node_memory < 512" -l) )); then
           log_pass "lication memo is good (${node_memorB)"
     Response(( $(echo "$nod 1024" | bc -lthen
     ng_test og_warn "Applicatiose Timey usage is hig lseode_memory}MB)"
  "al a else
         rl log_warn "Applica"$on memory usar  logthenigh (${node_memory}M
        fi
    elselocal response_mcho "$response_tim" | bc | cut -d. -f1)
  g_warld not determine application memor
    fi
}

va    ity"; thity_configplicaon() {
    log elstion "Securitguration Validation"
 " 2        log_waMEttApplication respoI --mime is slow (${respocal he ms)"
    # Test ewall Statu
    log_test "est 4: Secl Status
    if command -"Apnot r/dev/nulg app_url"en
        local u(ufw status | head -1 | $2}')
        e c may not redirectP to HTTPS"HTTog_warn        le
    #   log_info orge by Applicatatus"
     og_test "Ap is ation Memory UsTPS TP pass "HT   log_  n
        if [[ _mufw_statuspon "active" ] "node.*server.js" grep -v grep | 6} END {print sum/1ev/null)
            log_pae_memorv/N&ewall is active"
        if (( $(echo "P"HTTloemory < 5ect | bc -l) ));
            # Check specific rulesemory usage is good (${ny}MB)"
            ifo "$nodatus | grep -ql) ) then; then
        log_warnlog_pass "SSH acce is ule configured"
            else
            log log_warn "SSH acc (${node_not found"
         
            
        l   if ufw status | grep -q "mogecp\|443/tcthen
                _pass rules configure
}     else
     " ]];      log_warn  -   eliTPS access rules nd"
            fi
     e" ]];ouon "Security Configuratio [[ )ion"
            v/nulail "UFW fer09 -nois not active" |nul" 2>/dev/43N_NAME:4"$DOMAI" -connect NAME"$DOMAIN_name ervers_client -sl o | openss_info=$(echlocal ssl  n
    #   fi
    elsetes"lidatioin wall Status"
    log_teg_fail "UFW firewallull 2>&1; then
    # 
    
    # Test 2: Fnufw_sn Status
    l   test "Fail2Ba"UFW fir
    if command -v fan-client > /dev/n1; then
            # Chare specificve fail2ban > /dev/; then
          | log_pass cFail2Ban is 
            
            # C.41\.2ail status
        ef  local active_jails # C   rule-client status 2>/dev/rep "Jt -d: -f2 | xar
            
            $active_jails" ]]; t
                fw stato "Active jaip\|cp"; tve_jails"
    if          log_pass "Fail2BaTPS access  configunfigureddomain_i  local"
            else
                logewarn "No active Fail2Bauldails found"
           fi
        else
            log_warn "FeW fian is install aot active"
        fi
    else
        log_warn "Fail2Ban not "
    fi
    
    # Test 3: SSL Certificion
    l u_test "SSL CertiftatuH prourity"
       [[ "$SSL_v fail2= "letsencrypt" ]]; hen
        local certtl SS="/etc/letse2ban >/live/$DOMAIN_NAME/fnpem"
        hd_ log_pass "Fail2Ban is grep -"
        if [[ -f "$cert ]]; then
            # Cal cert_expiry=09 -enddate -noout -in -d= -f2)
            local tive_jaepoch=$(date -dstatus"Jexpiry" +% c
            local currcatiautheH (date +%s)
         lse ocal days_until_expijae_jailexpiry_epoch - epoch) / 86400 ))
  sab       
            els_configno" "ertificate issuer: $(dA^Pp -q " g -issuer -n-in "$cert_path2)"
                logo "SSL certcticate expires ils fous_until_expir
            
        e[ $days_u_expiry -gt 30 ]]; then
            log_log_pass "SSL certinstalled  valid fotil_expiry days"
            elif [[ $dtil_expiry  7 ]]; then
           log_warn "SSL te expires in $til_expiry days - renewal nee
            else
        oot logilog_fail "SSL ce]; ticate expires$days_until_expiry d renewal needed"
            fi
    # Tes_se
            onf Secut"Let's Encryprityigificate not foupath"
    if [[ 
      local ceSSL_MODE"/etcpt/lidflare" ]]; then
      don    ck Cloud thee SSL
        al ce ssl_info=509 -end opensslrt_path|t -servername "$DAME" -connectOMAIN_NAME:443ev/null | op09 -noout -dev/null
        
        if [[ "$ed_prren" =~ "Cloudate +" ]]; then
            log_pass "SSntcertificate issp- curreloudflare"))
    ual elif"$ssl_info" ]]; th
     en"    log[[nfo "SSL certixpirtuaissuer: ; thenfo"
       le_pathg_pass "SSL certificificatain is valid"r $days_until_expiry 
        else
      *:}"warn _warn "Could not vn $days_unt certificate chain"
  perm="  
    else
        e   fiknown SSL mode:"
    fi
            log_warnncrypt certifate nound at $cert_path
      Test ginx/nginetHTTPS    "rect
 sshd_if [[ ""HTTP to HTTPS Redudflen
    local http_respsing Clcurl -SSLI -L --max-time 10 lly"DOMAIN_NAME"l | head -1)
    
    if eesponse" =~301"|"302" ]]; th
    le  log_pass "HicP to HTTPSode: $SSL_Ms configured"
 Seculse
        log_warn "TPS redirect onfigured"
    # 
    
    # T e"ticaSecurity Headers
     edfiSSL modssrity Headers V"
    local headersdow:640 -s -I --max-time 10 tps://$DOMME" 2>/dev/null
    
    if echo "$nxonf:644"trictransport-sechen
        log_pass HSTS header is present
    else
    fofilog_warnm in tical_er not found"
    f   local file_path="${fr $daysm%:*il
        local experm="${file_p*:}"
    if echo "$headerstil_grep -qi "xs iexpireifons"; then "SSL ceg_wa    lo     hen
        log_pasf "X-Frame-Options theder is present
      se
      ];     "X-Frame-Oeader not found"
    fi
    
       $(dat"$headers" | greontent-type-options"; th
        log_pa  l"X-Content-Type-Optisions may bis present"ssive: $file_path (ual_perm, expec$expect
        
        log_ath-f "$Content-Typheader not f
    fi
}

validateiost "SSH e_integration() {
    log_section/"Cloudflare Integratin"
    
    d"n #  1: DNS Resolution
        if t "DNS Resolutionno" "$sshd_conAME"
       al domain_ip=$(dig +shgin "$DOMAIN_NAME2>/dev/-1)
    
     f [[ -n "$domSSH root l; then
        log_pass "Din resolvmain_ip"
        
        # Check if itcaonnge
        if [[ p -q "^P_ip" =~ ^(104\.1[6ion nlog     d_config3[0-1]|108"2\.1[9][2-9\.2[0-4][0-\.162\.2[572\.64\.|172\.6[5-9]|2\.7[0-9]|173.245\.4[8-9\.245\.[5-9][0|188\.114\.93\.2[4-5][0-5]|197\.2[4-5][0-9]|1\.234\.240\.[0-9]|198\\.12[8-9]|198-9][0-9]|198\.415]) ]]; then
            log_pass "Domain is using Centis disable"
        else
            log_waro "Domain IP may no greicationCloudflare CDNst
        fi
    else
        # Checil "Domain  ersioot re
    fi
    
    # T et 2: SSL Certificate Chain
     o2Ban StaSSL Certificate Vaversionn"
    if fiv opensdev/null 2>&1; then
        local sslo | openssl s_client -name "$DOMAIN_NAME" -connecNAME:443" 2>openssl x509 -nooutr 2>/dev/null)
        log_warn "SSH confiU lation fi      elsund"
    f   if [[ "$ssl_infooudflaren
            "SSL certifued by Cloudf
      elif [[ -n "$ssl_info" n
       e_cloudflinfo "SSL certificate issuer: $_info"
       nfiguion_warn dfSL certificate s " issuepas       llare"
       
            log_warnuld not validaificate chain"
     "DNS Resolution for $DOAME"
    fi
}

valid   tc-nitoring_syg statu {
    log_secteck if ittori       ems Validation"
    
    ve      : Log Files Accessoudflay
    log_test ystem LoFiles Accessibty"
    lonfo "Domaines=(
        "/v/access.l
        "/var/lctive_sx/error.log""$ufw     if   }')
        "/var/loead -1 | sql/postgresql-*/"
        g/auth.log"
     tus"/var/log/syslog"
   :# Test 2: SSL Cer    Chain
    log_test "ficate Chain Vaion"
    ifr log_file in "${loit /dev/nul"; do
        if ls $log_nfo=$( /dev/null 2>&1;nt -IN_NAME" -connect ME:443null | opensuer 2>/dev/null
va          local log_s-h $log_file | cut -f
        "$sslog_pass "Log fiare"; thsible: $log_fi($log_size)"
          s "SSL certifte issued by oudflare"
            locatarn "Log file " CN ound: $lwa
            log_info " certificater: $ssl_info"
    eone
    
    #    t 2: Disk Space fidate s
    log_temory}rate (${ge is mok Space"
    localge=$(df -hog | awk 'NR==2 {prin}' | sed 's/%//')
    
    if [[ $log-lt 80 ]]; n
     Log directory disk uacceptablog_disk_usage}%)"
    ivse
    log_morywarn "Log directRediresk usage is hlog_disk_usage}%
    fi
    
    [[ "$http_res Rotation Coration
    log lost "Log Rog ono HTonfiguration"
    if [[ -f "/etc/rotate.d/nginx" ]]; the
        log_pass "HTTP toPS rotation is configur
    else
    arn "Nginx logot found"
      
    
    if [[ -f "/etc/logrotate.d/postgresql ns "httpn
        ls -lt 5 "PostgreSQL lo $r     on is config"
    else
  . | b log_warn "PSTS head* log rotationms=$(found"
    fi
        log_warn "HSTS head> /dev/pp_ud"
   st Test 4: System Resource Mtoring Capabiliti
    log_test  Resource Monitoring Cap
    if command -v htop > /dev/null 2>&1; th; th
        log_pass "htocess monitoring"
    else
        log_info "htop header nalled (option
    fi
    
    if echo nd -v io.pass "dev/null 2>&1;ent-tys"; th
        log_pass "iot /der.jTyplable for I/Oreonitoring"
    else
        log_wape-Oiotop not installed 
    fi
    
command -v netstat > /dev/null
        log_pass ng_sytat is available fmonitoring
    log_section "Monnline" == "tems Validation"
   wn"c log_info " e>/tat not isttalled (using [0 instead)"
    fi
}

    "/vnginx/access.log"{
    log_salid/logBackup Syste.onValidatio     log_semance() {
n     oratioidate/postgresql/postgressql.log"
  /lo Test 1: Backup Directo
    log_test "Backup Di Structure"
    fil backup_dir
}n"     "/var/lib/hauldeployment/backup
    fo  "/var/backups"_fi]}"; do
        "/tmp/hauling-deployment-tests"
    )  siataba"al log_siogn$(du -h $log_fi-f1 | head -1
            log_passzen "$file accessible: $log_file g_size)"
    for backup_dir i/m'));"ckup_dirs[@]}"; do
    l    f [[ -d "$backup_dile not then
           kup_count=$(find  -type me "*.json" -o ql" -o -nam.gz" 2>| wc -l)
    do      log_pass ectory exists: $backr ($backcount files)"
        else
  Post      log_infSpace d notirectory nod: $backup_dir
    log"Log Direck Space"
    done
    
    if [[ $log_disk_se Backup Cap; t
    log_testpassaccene Backup Capability"
    if cg_dump > /dev/nullhen
        log_udo -u postgres pg_d msk usStgion > /dev/nog_    1; then
            log_paeSQL backu
    nne else
        t   log_warn "iostgreSQL backup toolssible"
        ffrs:"Log Rot__buffConfishared"Po
    else
        log_parn "pg_dump norotaund"
     ]]; theg_conf[[ -f "$pf       il.conf"
    
    fion" ]]: Configuratio -Backup C
    log_test "Confiration Backup Cability"
    local config_files=(
        ati Cassinx/nginx.QL l   rotation is config
        "/etc/ngiavailable/hauling-qr-
        ".env"
    fi
    
    local b  n}p_possiblece Monitorg
    log_test slow ( tResource Monittabag Capabilitie "D          llse
    for confip > /devn "${config"; do
        if [[}ms)dhtop nfig_file" ]];or process me cooring"
            backut 500 $duratiof [[        elis)"
u           break
        fi
        
    
    if [[o"$backup_pdatcal end_lable]]; then
        log"Configuraes are avaifor backup"
     lse
        rn "Some configuras are missing"
    
    
    # Test 4: ss "netstarage Space
    eog_test up Storage 
    local backuo "nvie=$(df -h /var  "d k 'NR==2 {print $4)
    local backupbackup_space" | sed 
    elnningrvice is ruL sePostgreSQ log_pass "       1; then
2>&ull (( $(echo "$backup_restl is-acti | bc -l) )); 
        logckup_sysdequate bge space ackup_space)
    le S
        log_warimited backup spbackup_space)"
   on"idot 1: Backup DirDataby Structurtion e() {
    lrforest "Backup Directory 
}cal backup_dir
    "/vainerformance_security_report {
    log_section "Gen Comprehensive Report"
        on not fouing-deployment-tests"
    cat > "$REPORT_FILE
  ==============================
Performanceackup_possiby Validation Report
==  for backup_================; do

Generated: $(date)
D       $DOMAIN_NAME
SSL Mod     backup_possible=tru
 uration: $PERFORMTEST_DURATIonds
Validatioi $SECURITY_SCAN_

==========================
Sys if [[ "$mation
=====================ku=================
    fime: $(hostname)
OS: $(lsb_release null | cut -cat /etc/os-release | PRETTY_NE | cut -d'"' -
  rnel: $(uname -r)
CPU Cores: $(nDatabase"
 emory: $(fand -v | grep Mem | awk '{print $2}'    lel, $(free -h | gp Mem | awk '{pri $4}') available
  sk: $(df -udo - awk 'NRg_dump --version > /d1, $(df - | awk 'NR==2 {available

===============================
       ance Metricrn ressginx GL backu"N    lonot accessibl
 ====================================
CPU LoTe$(uptime | awk -' '{print $2}' | xar
       Usage: $(free -h'{printf "%.1f%%$2 * 100}')
Diskfidf -h / | awk 'NR==2 {p
Network Latency:-c 3 8.8 2>/dev/null | tail -k -F'/' '{print cho "N/A")
    # Test 3: Cration Backu
    s)cti================on Backupn=====
   urity Status
===     "/etc/)orker_canx.conf"========
Fi    ]; th(ufw status 2>-avainull | head -1 | awk 'm"int $2}' || echo ")
Fail2Ban: $(systemctl ie fail2ban 2>/devecho "Not
  H Root Login "^PermitRootLogin" /etsshd_config null | awk '{t $2}' ||"Unknown")
      rtificate: $(if sse"$SSL_MODE" ==p"cloudflare" ]]; cesses wo "CloudNgi l"; elif         etc/letsencrypt/live/$DOMAIN_Nn.pem" ]]; then e"; else echo own"; fi)

======= cer_ig_file i=====cod ig_=======
Servis" ]];e "
============backu==================
   nx: $(systemctive nginx 2>/dev/null || e"Not active")
    ;//'QL: $(systemct $2}' | awk postgresql 2>"x_conf"  || echo "Not activceE "workerp -sses=$(greprocel worker_ locan
    .js App: $(if pgrep "node.*server/dev/null; then eng"; else e "Nive"; fi)
PM2:if [[ command -ngin2 > /de/x_conf="/ng&& pm2 list 2>//null | grene"; then "Running"; elso "Not acti)

=============ass "Config================
Recommendat
=================e configur f===========
GENEfi:
- Regular sring for p
  Keep all software conts updated
- Implerserra"Bomated bacagx Spocedures
- Revocalsecurity cace=$(df k 'NR==iodically
- Monitor bdL cenfificate expirati$badates
- Implemnitoring and aler

EOF

    # Add wted backup space test results
    ff [[ $TESTS_WARNINGPNginecti; then
        eNINGS FOU" >> "$REPORT_FILE
echo "- $TErated warnings" >> "$E"
        epfin"- Consider address for optrmance" >> "$REPLE"
        eectra" >> "$REPORTILE"
    fi
    
    # A====  fi========es based on test rts
    if [[ $TESTS_g_hiED -gt 0 ]]; thenport
======= echo "CRITICAL IOUND:" >> T_FILE"
        "- $TESTS_FAILEs failed - immeention required" >> "EPORT_FILE"
Gene)   echo "- Revifailed testslement f>> "$REPORT"
        print"" >> EFILE"
    fi

    log_pass "eetwork nsive report gen $REPORT_FILE"
}

# C===%nd line ar=========rsing
whi ageest# -gt 0 ]]; do
Pa  case $1 in
      ]];TESTS_FAILED
Warnidis: $TESMAIN_NAME="$2"
  ccess R   shift 2
        
====    --ssl-mode)
 yst        SSL_MODE="$2
=======     shift======  # Te===========
Hos         ;;
       at /etc/os-rele | grep PRETTY_NAME | cut= -f2 | cut -
Kernel:     PERFORMANcal (${age isION="y us "M  log_fail      else
            shif)2
          age_p${mem_high (ry usage is Memo log_warn "       hen
 emo    --security| grsag
            SECUR awk 'e (${mTH="$2"
  shif
===         ;;
        --r Mort-file)
   {p       Rm ========="$2"
     oadrint hift 2
           ee AUsagry mo "grepst   | awk '{print $3}') / $ree -h | grep Mprint $2}')
 isk U  --help)
         $(ping -c sage: $0 [optio awk -F'|| echo "N/
            echo "Opt
  es co     echo "  --domain D===IN    Domain name CPghog_warn ault: truckhlse
            echo "   MODE    sencrypt o (default: lare)"
            echo "  --d==========CONDS  Performancess "CPU   loon (default:
    wall:   echo "  --secpuad -1 | h LEVEL  Security "Unkno basic or detailed (debasic)"
     f      echo "  --report-file F| echcutput reporfile"
  H Roots/,// : $(grephelp       awkogins help mes/s averanfig 2>/dev/ uptime |ad_avgnknown")
SSL         exit 0(if [[ "oca" == "cloudthen echooudflare"; elif [[ -/etc/letsencryptIN_NAME/fullchain.pem" ]]; then echot's Encrypt"; fi)
         ;;
        *)= 1===================
  n"tus  echo "Unkn $1"
         == echo "Use --help ==ion age information"
        (systemctl is-ace_systvalidon/dev/null || echo "Not acti
nctinreS    ;;
    .js App:grep -f "nod" > /dev/ 2>&1; then eching";  echo "Not aci)
PM2: $ -v pm2 > /dev/null  && pm2 list 2>/| grep -q "online"; then ecnning"; else eot active"; fi)

=======ll required tools ====esent
if ! command -v bc > /d; then
 og_==============alling bc calcula..."
    apt-get update -qqS:-get install -y/dev/null 2>&1
fi

# Maiemtxecution function
 Revi) {
    log_h ED++"rtifiIPT_NAME v$VERSION"
- Implement log monCRED}[F-end alerting
    log_info tarting performance and salidation..."
    loilD++))"Domain: $ME"
    loSSL_MODE
(TESTog_info "Test Duratiogt 0 ]]; tEST_DURATION s
        echo "WRE"$-GS FOUND:" >ss()REPORT_FILE"
    # Run validatioG tests generated wars" >> "$REP
        eng warnings for ooance
        echoEPORT_FIe
    validate_databasece
    vaate_application_performa
    ialidate_security_configuration
    valiSSUES FO dflare_intE"tion
    validah"- $TESTS_ng_systems
        echo "ackup_1 e "\s
    
    # Generateve repo
erate_performance_sort
    
    # Final su
ader "Test R
#   echo -e "${GREEment pars $TESTS_PASSED${NC
woncecho -e "${RE ]]; do: $TESTILED${NC}"
    ccho -e "$ings: $TESTS_WARNING"
    echo -eelp(dLUE}Total: $TESTS_TormanceC}"
    
    local CE_TEss_rate=$(( TES* 100 / TESTS_TO ))
   FORMo -e "cho "  -uccess Rate: ${sucDomairate}%${NC}"
      kha   echo "  --DOMAINNAME=DE    SSL mo: letsenr cloudflare (default: re)"
    echo -e "\no   --durport Location: ${REPOn (defaul{NC}"
    
         t with appropriate coE  Output reile"
    if [[ $TESTS_F  --h -eq 0 ]]his en
            exit\n${GREEN}✅ All crists passed!$
        exit 0
   TS se
            DOMA"\n${RED}❌ ests failed. Reviport for deta"
        exit 1
   N='\033[0;;
        --ssl-

# Run mai   3[ift 2
main      ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Run main function
main