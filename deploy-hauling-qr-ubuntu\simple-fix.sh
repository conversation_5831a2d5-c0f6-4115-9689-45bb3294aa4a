#!/bin/bash

# Add missing functions to deploy-hauling-qr-ubuntu.sh

echo "Fixing deploy-hauling-qr-ubuntu.sh..."

# Add generate_strong_password function
if ! grep -q "^generate_strong_password()" deploy-hauling-qr-ubuntu.sh; then
    echo "Adding generate_strong_password function..."
    cat >> deploy-hauling-qr-ubuntu.sh << 'EOF'

# Function to generate a strong random password
generate_strong_password() {
    local length="${1:-16}"
    local password=""
    
    # Ensure we have the required tools
    if command -v openssl &> /dev/null; then
        # Generate random password using OpenSSL
        password=$(openssl rand -base64 $((length * 2)) | tr -dc "a-zA-Z0-9!@#$%^&*()_+{}|:<>?~" | head -c "$length")
    else
        # Fallback to /dev/urandom
        password=$(cat /dev/urandom | tr -dc "a-zA-Z0-9!@#$%^&*()_+{}|:<>?~" | head -c "$length")
    fi
    
    echo "$password"
}
EOF
fi

# Add validate_password_strength function
if ! grep -q "^validate_password_strength()" deploy-hauling-qr-ubuntu.sh; then
    echo "Adding validate_password_strength function..."
    cat >> deploy-hauling-qr-ubuntu.sh << 'EOF'

# Function to validate password strength
validate_password_strength() {
    local password="$1"
    local min_length="${2:-8}"
    
    # Check password length
    if [[ ${#password} -lt $min_length ]]; then
        return 1
    fi
    
    # Check for at least one uppercase letter
    if ! echo "$password" | grep -q "[A-Z]"; then
        return 1
    fi
    
    # Check for at least one lowercase letter
    if ! echo "$password" | grep -q "[a-z]"; then
        return 1
    fi
    
    # Check for at least one digit
    if ! echo "$password" | grep -q "[0-9]"; then
        return 1
    fi
    
    # Check for at least one special character
    if ! echo "$password" | grep -q "[!@#$%^&*()_+{}|:<>?~]"; then
        return 1
    fi
    
    return 0
}
EOF
fi

echo "Fixes applied successfully!"