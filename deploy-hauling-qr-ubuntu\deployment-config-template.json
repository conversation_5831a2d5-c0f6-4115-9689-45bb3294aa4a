{"domain": {"name": "truckhaul.top", "sslMode": "cloudflare", "cloudflare": {"mode": "full", "apiToken": "", "zoneId": "", "optimization": {"minifyHtml": true, "minifyCss": true, "minifyJs": true, "brotli": true, "cacheLevel": "standard", "browserCacheTtl": 14400}}}, "database": {"password": "", "host": "localhost", "port": 5432, "name": "hauling_qr_system", "user": "hauling_app", "poolMax": 25, "poolMin": 5, "optimization": {"sharedBuffers": "256MB", "effectiveCacheSize": "1GB", "workMem": "4MB", "maxConnections": 100}}, "security": {"jwtSecret": "", "jwtExpiration": "24h", "jwtRefreshSecret": "", "jwtRefreshExpiration": "7d", "fail2ban": {"banTime": 3600, "maxRetry": 5}, "firewall": {"enable": true}, "rateLimiting": {"api": "20r/s", "auth": "5r/m", "general": "10r/s"}}, "admin": {"username": "admin", "password": "", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/your-org/hauling-qr-trip-system.git", "branch": "main"}, "features": {"monitoring": {"enabled": true, "healthCheckInterval": "*/5 * * * *", "performanceCheckInterval": "*/10 * * * *", "reportGenerationTime": "0 6 * * *"}, "backups": {"enabled": true, "schedule": "0 2 * * *", "retentionDays": 7, "fullBackupSchedule": "0 3 * * 0", "compression": true}}, "environment": {"mode": "production", "nodeVersion": "18", "postgresVersion": "15", "appUser": "hauling_app", "appDir": "/var/www/hauling-qr-system"}, "performance": {"pm2Instances": "max", "maxMemoryRestart": "2G", "nodeMaxOldSpace": 2048}, "logging": {"level": "info", "rotationSize": "100M", "retentionDays": 52}, "email": {"smtpHost": "smtp.gmail.com", "smtpPort": 587, "smtpUser": "<EMAIL>", "smtpPassword": "", "alertEmail": "<EMAIL>"}}