-- Active: 1752821799542@@localhost@5432@hauling_qr_system
-- ============================================================================
-- INSERT SAMPLE DATA QUERIES
-- QR Code-based Hauling Truck Trip Management System
-- ============================================================================
-- These INSERT statements will populate your new database with the exact
-- sample data from the init.sql file for locations, trucks, and drivers
-- ============================================================================

-- =============================================================================
-- INSERT LOCATIONS
-- =============================================================================

INSERT INTO locations (location_code, name, type, address, coordinates, status, qr_code_data) VALUES
('LOC-001', 'Point A - Main Loading Site', 'loading', '123 Industrial Ave, Loading District', '40.7128,-74.0060', 'active', '{"type":"location","id":"LOC-001","name":"Point A","coordinates":"40.7128,-74.0060","timestamp":"2025-01-01T00:00:00Z"}'::jsonb),
('LOC-002', 'Point B - Primary Dump Site', 'unloading', '456 Dump Rd, Disposal Area', '40.7589,-73.9851', 'active', '{"type":"location","id":"LOC-002","name":"Point B","coordinates":"40.7589,-73.9851","timestamp":"2025-01-01T00:00:00Z"}'::jsonb),
('LOC-003', 'Point C - Secondary Dump Site', 'unloading', '789 Alternative Way, Backup Site', '40.7282,-73.7949', 'active', '{"type":"location","id":"LOC-003","name":"Point C","coordinates":"40.7282,-73.7949","timestamp":"2025-01-01T00:00:00Z"}'::jsonb);

-- =============================================================================
-- INSERT DRIVERS
-- =============================================================================

INSERT INTO drivers (employee_id, full_name, license_number, license_expiry, phone, email, hire_date) VALUES 
('DR-001', 'John Smith', 'CDL123456789', '2026-12-31', '******-0101', '<EMAIL>', '2023-01-15'),
('DR-002', 'Maria Garcia', 'CDL987654321', '2027-06-30', '******-0102', '<EMAIL>', '2023-03-01'),
('DR-003', 'Robert Johnson', 'CDL456789123', '2026-09-15', '******-0103', '<EMAIL>', '2023-02-20');

-- =============================================================================
-- INSERT DUMP TRUCKS
-- =============================================================================

INSERT INTO dump_trucks (truck_number, license_plate, make, model, year, capacity_tons, qr_code_data) VALUES
('DT-100', 'TRK-001', 'Volvo', 'VHD', 2022, 15.5, '{"type":"truck","id":"DT-100","assigned_route":"A-B","driver_id":"DR-001","timestamp":"2025-01-01T00:00:00Z"}'::jsonb),
('DT-101', 'TRK-002', 'Mack', 'Granite', 2021, 18.0, '{"type":"truck","id":"DT-101","assigned_route":"A-B","driver_id":"DR-002","timestamp":"2025-01-01T00:00:00Z"}'::jsonb),
('DT-102', 'TRK-003', 'Peterbilt', '567', 2023, 16.5, '{"type":"truck","id":"DT-102","assigned_route":"A-C","driver_id":"DR-003","timestamp":"2025-01-01T00:00:00Z"}'::jsonb);


-- =============================================================================
-- INSERT SAMPLE ASSIGNMENTS
-- =============================================================================

INSERT INTO assignments (assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id, status, assigned_date, start_time) VALUES
('ASG-001-SAMPLE', 1, 1, 1, 2, 'assigned', CURRENT_DATE, CURRENT_TIMESTAMP),
('ASG-002-SAMPLE', 2, 2, 1, 2, 'assigned', CURRENT_DATE, CURRENT_TIMESTAMP),
('ASG-003-SAMPLE', 3, 3, 1, 3, 'assigned', CURRENT_DATE, CURRENT_TIMESTAMP);

