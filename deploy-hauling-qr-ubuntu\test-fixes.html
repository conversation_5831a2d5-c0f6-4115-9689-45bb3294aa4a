<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hauling QR System - Fix Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .mobile-test {
            border: 2px solid #007bff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        @media (max-width: 768px) {
            .mobile-test {
                background-color: #e7f3ff;
            }
        }
    </style>
</head>
<body>
    <h1>🔧 Hauling QR System - Fix Testing</h1>
    <p>This page tests the fixes for WebSocket connectivity and mobile sidebar scrolling issues.</p>

    <!-- WebSocket Test Section -->
    <div class="test-section">
        <h2>🔌 WebSocket Connection Test</h2>
        <div id="ws-status" class="status info">Not tested yet</div>
        <button onclick="testWebSocket()">Test WebSocket Connection</button>
        <button onclick="clearLogs()">Clear Logs</button>
        <div id="ws-logs" class="log"></div>
    </div>

    <!-- API Test Section -->
    <div class="test-section">
        <h2>🌐 API Connectivity Test</h2>
        <div id="api-status" class="status info">Not tested yet</div>
        <button onclick="testAPI()">Test API Endpoints</button>
        <div id="api-logs" class="log"></div>
    </div>

    <!-- Mobile Sidebar Test Section -->
    <div class="test-section">
        <h2>📱 Mobile Sidebar Scrolling Test</h2>
        <div class="mobile-test">
            <p><strong>Instructions for Mobile Testing:</strong></p>
            <ol>
                <li>Open this page on a mobile device or use browser dev tools mobile view</li>
                <li>Navigate to the main application: <a href="https://35cp9j1x-3000.asse.devtunnels.ms/" target="_blank">Open App</a></li>
                <li>Open the mobile sidebar (hamburger menu)</li>
                <li>Try scrolling through the navigation items</li>
                <li>Verify all menu items are accessible</li>
            </ol>
            <div id="mobile-status" class="status info">Test manually on mobile device</div>
        </div>
    </div>

    <!-- Network Configuration Display -->
    <div class="test-section">
        <h2>🌐 Network Configuration</h2>
        <button onclick="showNetworkConfig()">Show Network Config</button>
        <div id="network-config" class="log"></div>
    </div>

    <script>
        let wsLogs = '';
        let apiLogs = '';

        function log(message, type = 'ws') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            
            if (type === 'ws') {
                wsLogs += logMessage;
                document.getElementById('ws-logs').textContent = wsLogs;
            } else {
                apiLogs += logMessage;
                document.getElementById('api-logs').textContent = apiLogs;
            }
        }

        function clearLogs() {
            wsLogs = '';
            apiLogs = '';
            document.getElementById('ws-logs').textContent = '';
            document.getElementById('api-logs').textContent = '';
        }

        function updateStatus(elementId, message, className) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${className}`;
        }

        async function testWebSocket() {
            updateStatus('ws-status', 'Testing WebSocket connection...', 'warning');
            log('Starting WebSocket connection test...', 'ws');

            try {
                // Determine WebSocket URL
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const hostname = window.location.hostname;
                const port = window.location.port ? `:${window.location.port}` : '';
                const wsUrl = `${protocol}//${hostname}${port}/ws`;
                
                log(`Attempting to connect to: ${wsUrl}`, 'ws');

                const ws = new WebSocket(wsUrl);
                
                ws.onopen = () => {
                    log('✅ WebSocket connection opened successfully!', 'ws');
                    updateStatus('ws-status', 'WebSocket connected successfully!', 'success');
                    
                    // Send test message
                    ws.send(JSON.stringify({
                        type: 'test',
                        message: 'Connection test from fix verification page'
                    }));
                    log('📤 Sent test message', 'ws');
                };

                ws.onmessage = (event) => {
                    log(`📥 Received message: ${event.data}`, 'ws');
                };

                ws.onerror = (error) => {
                    log(`❌ WebSocket error: ${error}`, 'ws');
                    updateStatus('ws-status', 'WebSocket connection failed!', 'error');
                };

                ws.onclose = (event) => {
                    log(`🔌 WebSocket closed: Code ${event.code}, Reason: ${event.reason}`, 'ws');
                    if (event.code !== 1000) {
                        updateStatus('ws-status', `WebSocket closed unexpectedly (Code: ${event.code})`, 'error');
                    }
                };

                // Close connection after 10 seconds
                setTimeout(() => {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.close(1000, 'Test completed');
                        log('🔌 Test completed, closing connection', 'ws');
                    }
                }, 10000);

            } catch (error) {
                log(`❌ WebSocket test failed: ${error.message}`, 'ws');
                updateStatus('ws-status', 'WebSocket test failed!', 'error');
            }
        }

        async function testAPI() {
            updateStatus('api-status', 'Testing API endpoints...', 'warning');
            log('Starting API connectivity test...', 'api');

            try {
                // Test health endpoint
                const healthUrl = `${window.location.origin}/api/health`;
                log(`Testing health endpoint: ${healthUrl}`, 'api');
                
                const healthResponse = await fetch(healthUrl);
                if (healthResponse.ok) {
                    const healthData = await healthResponse.json();
                    log(`✅ Health check passed: ${healthData.message}`, 'api');
                } else {
                    log(`❌ Health check failed: ${healthResponse.status}`, 'api');
                }

                // Test trucks endpoint (will fail without auth, but should reach the server)
                const trucksUrl = `${window.location.origin}/api/trucks`;
                log(`Testing trucks endpoint: ${trucksUrl}`, 'api');
                
                const trucksResponse = await fetch(trucksUrl);
                log(`📡 Trucks endpoint response: ${trucksResponse.status} ${trucksResponse.statusText}`, 'api');
                
                if (trucksResponse.status === 401) {
                    log('✅ Trucks endpoint reachable (401 = auth required, which is expected)', 'api');
                    updateStatus('api-status', 'API endpoints are reachable!', 'success');
                } else if (trucksResponse.ok) {
                    log('✅ Trucks endpoint accessible', 'api');
                    updateStatus('api-status', 'API endpoints working!', 'success');
                } else {
                    log(`⚠️ Unexpected trucks endpoint response: ${trucksResponse.status}`, 'api');
                    updateStatus('api-status', 'API endpoints partially working', 'warning');
                }

            } catch (error) {
                log(`❌ API test failed: ${error.message}`, 'api');
                updateStatus('api-status', 'API test failed!', 'error');
            }
        }

        function showNetworkConfig() {
            const config = {
                hostname: window.location.hostname,
                protocol: window.location.protocol,
                port: window.location.port,
                origin: window.location.origin,
                userAgent: navigator.userAgent,
                isDevTunnel: window.location.hostname.includes('devtunnels.ms'),
                isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
            };
            
            document.getElementById('network-config').textContent = JSON.stringify(config, null, 2);
        }

        // Auto-run network config on page load
        window.onload = () => {
            showNetworkConfig();
        };
    </script>
</body>
</html>
