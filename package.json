{"name": "hauling-qr-trip-system", "version": "1.0.0", "description": "Hauling QR Trip Management System", "main": "server/server.js", "scripts": {"start": "node server/server.js", "dev": "nodemon server/server.js", "test": "jest", "db:migrate": "node database/run-migration.js"}, "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "pg": "^8.11.0", "ws": "^8.18.3"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^2.0.22", "supertest": "^6.3.3"}, "keywords": ["hauling", "qr", "trip", "management", "logistics"], "author": "Hauling QR System Team", "license": "MIT"}